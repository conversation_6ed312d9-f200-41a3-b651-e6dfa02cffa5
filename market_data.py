"""
Market Data Handler for Real-time Data Feeds
Manages data fetching, caching, and formatting for technical analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time
from kiteconnect import KiteConnect
from kiteconnect.exceptions import KiteException

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketDataHandler:
    """
    Handles real-time market data fetching and management
    """

    def __init__(self, kite: KiteConnect, trading_mode: str = "TESTING"):
        """
        Initialize Market Data Handler

        Args:
            kite: KiteConnect instance
            trading_mode: "TESTING" or "LIVE"
        """
        self.kite = kite
        self.trading_mode = trading_mode.upper()
        self.logger = logger

        # Data cache
        self.data_cache = {}
        self.last_update = {}

        # Default instruments
        self.default_instruments = {
            "NIFTY": "NSE:NIFTY 50",
            "BANKNIFTY": "NSE:NIFTY BANK"
        }

        self.logger.info(f"Market Data Handler initialized in {self.trading_mode} mode")

    def get_historical_data(self, instrument_token: str, from_date: datetime,
                          to_date: datetime, interval: str = "5minute") -> pd.DataFrame:
        """
        Fetch historical OHLC data

        Args:
            instrument_token: Instrument token or symbol
            from_date: Start date
            to_date: End date
            interval: Data interval (minute, 5minute, 15minute, hour, day)

        Returns:
            DataFrame with OHLC data
        """
        try:
            # Always fetch real data from Zerodha (even in TESTING mode)
            # TESTING mode only affects order placement, not data fetching
            if not self.kite:
                self.logger.warning("No Kite connection available, generating mock data")
                return self._generate_mock_data(from_date, to_date, interval)

            # Fetch real data from Zerodha
            data = self.kite.historical_data(
                instrument_token=instrument_token,
                from_date=from_date,
                to_date=to_date,
                interval=interval
            )

            if not data:
                self.logger.warning(f"No data received for {instrument_token}")
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)

            # Rename columns to standard format
            df.columns = ['open', 'high', 'low', 'close', 'volume']

            self.logger.info(f"Fetched {len(df)} candles for {instrument_token}")

            return df

        except KiteException as e:
            self.logger.error(f"Kite API error fetching historical data: {e}")
            # Fallback to mock data if API fails
            self.logger.info("Falling back to mock data generation")
            return self._generate_mock_data(from_date, to_date, interval)
        except Exception as e:
            self.logger.error(f"Error fetching historical data: {e}")
            # Fallback to mock data if any error occurs
            self.logger.info("Falling back to mock data generation")
            return self._generate_mock_data(from_date, to_date, interval)

    def get_live_data(self, symbol: str = "NIFTY", interval: str = "5minute",
                     candles: int = 100) -> pd.DataFrame:
        """
        Get live/recent market data

        Args:
            symbol: Symbol to fetch data for
            interval: Data interval
            candles: Number of recent candles to fetch

        Returns:
            DataFrame with recent OHLC data
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{interval}"
            current_time = datetime.now()

            if (cache_key in self.data_cache and
                cache_key in self.last_update and
                (current_time - self.last_update[cache_key]).seconds < 60):  # 1 minute cache

                self.logger.debug(f"Returning cached data for {symbol}")
                return self.data_cache[cache_key]

            # Calculate date range
            to_date = datetime.now()

            # Calculate from_date based on interval and required candles
            if interval == "minute":
                from_date = to_date - timedelta(minutes=candles + 10)
            elif interval == "5minute":
                from_date = to_date - timedelta(minutes=(candles * 5) + 50)
            elif interval == "15minute":
                from_date = to_date - timedelta(minutes=(candles * 15) + 150)
            else:
                from_date = to_date - timedelta(days=candles + 5)

            # Get instrument token
            instrument_token = self._get_instrument_token(symbol)

            # Fetch data
            df = self.get_historical_data(instrument_token, from_date, to_date, interval)

            if not df.empty:
                # Keep only required number of candles
                df = df.tail(candles)

                # Update cache
                self.data_cache[cache_key] = df
                self.last_update[cache_key] = current_time

            return df

        except Exception as e:
            self.logger.error(f"Error fetching live data for {symbol}: {e}")
            return pd.DataFrame()

    def get_current_price(self, symbol: str = "NIFTY") -> float:
        """
        Get current market price

        Args:
            symbol: Symbol to get price for

        Returns:
            Current price
        """
        try:
            # Always fetch real price (even in TESTING mode)
            if not self.kite:
                self.logger.warning("No Kite connection available, returning mock price")
                return 21500.0 + np.random.uniform(-100, 100)

            # Get instrument token
            instrument_token = self._get_instrument_token(symbol)

            # Get LTP
            quote = self.kite.ltp([instrument_token])
            price = quote[instrument_token]["last_price"]

            return price

        except Exception as e:
            self.logger.error(f"Error fetching current price for {symbol}: {e}")
            # Fallback to mock price if API fails
            return 21500.0 + np.random.uniform(-100, 100)

    def get_quote(self, symbol: str = "NIFTY") -> Dict:
        """
        Get detailed quote information

        Args:
            symbol: Symbol to get quote for

        Returns:
            Quote dictionary
        """
        try:
            # Always fetch real quote (even in TESTING mode)
            if not self.kite:
                self.logger.warning("No Kite connection available, returning mock quote")
                base_price = 21500.0
                return {
                    "last_price": base_price + np.random.uniform(-50, 50),
                    "open": base_price + np.random.uniform(-30, 30),
                    "high": base_price + np.random.uniform(0, 100),
                    "low": base_price + np.random.uniform(-100, 0),
                    "close": base_price + np.random.uniform(-20, 20),
                    "volume": np.random.randint(1000000, 5000000),
                    "timestamp": datetime.now()
                }

            # Get instrument token
            instrument_token = self._get_instrument_token(symbol)

            # Get quote
            quote = self.kite.quote([instrument_token])
            quote_data = quote[instrument_token]

            return {
                "last_price": quote_data["last_price"],
                "open": quote_data["ohlc"]["open"],
                "high": quote_data["ohlc"]["high"],
                "low": quote_data["ohlc"]["low"],
                "close": quote_data["ohlc"]["close"],
                "volume": quote_data["volume"],
                "timestamp": datetime.now()
            }

        except Exception as e:
            self.logger.error(f"Error fetching quote for {symbol}: {e}")
            # Fallback to mock quote if API fails
            base_price = 21500.0
            return {
                "last_price": base_price + np.random.uniform(-50, 50),
                "open": base_price + np.random.uniform(-30, 30),
                "high": base_price + np.random.uniform(0, 100),
                "low": base_price + np.random.uniform(-100, 0),
                "close": base_price + np.random.uniform(-20, 20),
                "volume": np.random.randint(1000000, 5000000),
                "timestamp": datetime.now()
            }

    def _get_instrument_token(self, symbol: str) -> str:
        """
        Get instrument token for symbol

        Args:
            symbol: Symbol name

        Returns:
            Instrument token
        """
        try:
            # Always use real instrument tokens (even in TESTING mode)
            if symbol in self.default_instruments:
                return self.default_instruments[symbol]

            if not self.kite:
                self.logger.warning(f"No Kite connection available for {symbol}")
                return symbol

            # Search in instruments list
            instruments = self.kite.instruments()
            for instrument in instruments:
                if instrument['tradingsymbol'] == symbol:
                    return instrument['instrument_token']

            # If not found, return the symbol itself
            return symbol

        except Exception as e:
            self.logger.error(f"Error getting instrument token for {symbol}: {e}")
            return symbol

    def _generate_mock_data(self, from_date: datetime, to_date: datetime,
                           interval: str = "5minute") -> pd.DataFrame:
        """
        Generate mock OHLC data for testing

        Args:
            from_date: Start date
            to_date: End date
            interval: Data interval

        Returns:
            Mock OHLC DataFrame
        """
        try:
            # Calculate time delta based on interval
            if interval == "minute":
                delta = timedelta(minutes=1)
            elif interval == "5minute":
                delta = timedelta(minutes=5)
            elif interval == "15minute":
                delta = timedelta(minutes=15)
            elif interval == "hour":
                delta = timedelta(hours=1)
            else:  # day
                delta = timedelta(days=1)

            # Generate time series
            dates = []
            current = from_date
            while current <= to_date:
                # Skip weekends for daily data
                if interval == "day" and current.weekday() >= 5:
                    current += delta
                    continue

                # Skip non-trading hours for intraday
                if interval in ["minute", "5minute", "15minute"]:
                    if current.hour < 9 or current.hour >= 15:
                        current += delta
                        continue
                    if current.hour == 9 and current.minute < 15:
                        current += delta
                        continue

                dates.append(current)
                current += delta

            if not dates:
                return pd.DataFrame()

            # Generate mock price data
            base_price = 21500.0
            data = []

            for i, date in enumerate(dates):
                if i == 0:
                    open_price = base_price
                else:
                    open_price = data[i-1]['close']

                # Add some randomness
                change = np.random.uniform(-0.5, 0.5) / 100 * open_price
                close_price = open_price + change

                high_price = max(open_price, close_price) + np.random.uniform(0, 0.2) / 100 * open_price
                low_price = min(open_price, close_price) - np.random.uniform(0, 0.2) / 100 * open_price

                volume = np.random.randint(100000, 1000000)

                data.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })

            df = pd.DataFrame(data)
            df.set_index('date', inplace=True)

            return df

        except Exception as e:
            self.logger.error(f"Error generating mock data: {e}")
            return pd.DataFrame()

    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """
        Validate quality of OHLC data

        Args:
            df: OHLC DataFrame

        Returns:
            Validation results
        """
        try:
            if df.empty:
                return {"valid": False, "errors": ["Empty dataset"]}

            errors = []
            warnings = []

            # Check required columns
            required_cols = ['open', 'high', 'low', 'close']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                errors.append(f"Missing columns: {missing_cols}")

            # Check for null values
            null_counts = df[required_cols].isnull().sum()
            if null_counts.any():
                warnings.append(f"Null values found: {null_counts.to_dict()}")

            # Check OHLC relationships
            invalid_ohlc = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close'])
            )

            if invalid_ohlc.any():
                errors.append(f"Invalid OHLC relationships in {invalid_ohlc.sum()} rows")

            # Check for extreme price movements
            if len(df) > 1:
                price_changes = df['close'].pct_change().abs()
                extreme_moves = price_changes > 0.1  # 10% moves
                if extreme_moves.any():
                    warnings.append(f"Extreme price movements detected in {extreme_moves.sum()} candles")

            # Check data continuity
            if len(df) > 1:
                time_gaps = df.index.to_series().diff()
                expected_gap = time_gaps.mode().iloc[0] if not time_gaps.mode().empty else timedelta(minutes=5)
                large_gaps = time_gaps > expected_gap * 2
                if large_gaps.any():
                    warnings.append(f"Data gaps detected in {large_gaps.sum()} places")

            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "data_points": len(df),
                "date_range": {
                    "start": df.index.min().strftime("%Y-%m-%d %H:%M:%S"),
                    "end": df.index.max().strftime("%Y-%m-%d %H:%M:%S")
                }
            }

        except Exception as e:
            self.logger.error(f"Error validating data quality: {e}")
            return {"valid": False, "errors": [str(e)]}

    def clear_cache(self):
        """Clear data cache"""
        self.data_cache.clear()
        self.last_update.clear()
        self.logger.info("Data cache cleared")

    def get_cache_status(self) -> Dict:
        """
        Get cache status information

        Returns:
            Cache status dictionary
        """
        return {
            "cached_symbols": list(self.data_cache.keys()),
            "cache_size": len(self.data_cache),
            "last_updates": {
                key: value.strftime("%Y-%m-%d %H:%M:%S")
                for key, value in self.last_update.items()
            }
        }

def create_market_data_handler(kite: KiteConnect, trading_mode: str) -> MarketDataHandler:
    """
    Factory function to create MarketDataHandler instance

    Args:
        kite: KiteConnect instance
        trading_mode: Trading mode ("TESTING" or "LIVE")

    Returns:
        MarketDataHandler instance
    """
    return MarketDataHandler(kite, trading_mode)