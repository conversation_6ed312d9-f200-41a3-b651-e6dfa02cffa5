"""
Environment Setup Utility for Zerodha Trading Bot
Handles .env file creation and token management
"""

import os
from dotenv import load_dotenv, set_key

def create_env_file():
    """Create .env file with default configuration"""
    env_content = """# Zerodha API Credentials
# Get these from https://kite.zerodha.com/developers/
ZERODHA_API_KEY=your_api_key_here
ZERODHA_API_SECRET=your_api_secret_here
ZERODHA_ACCESS_TOKEN=''

# Trading Configuration
TIMEFRAME=5minute

SUPERTREND_PERIOD=5
SUPERTREND_MULTIPLIER=3

RSI_PERIOD=14
RSI_BUY_THRESHOLD=60
RSI_SELL_THRESHOLD=45

# Trading Hours
TRADING_START_TIME=09:15
TRADING_END_TIME=15:15

# Trading Mode (TESTING or LIVE)
# TESTING: Orders are simulated, no real trades executed
# LIVE: Real orders are placed in the market
TRADING_MODE=TESTING

# Risk Management
MAX_TRADES_PER_DAY=2
STOP_LOSS_PERCENTAGE=20
POSITION_SIZE=75

# Logging
LOG_LEVEL=INFO
LOG_FILE=trading_bot.log
"""

    with open('.env', 'w') as f:
        f.write(env_content)

    print("✅ .env file created successfully!")
    print("📝 Please update your API credentials in the .env file")

def update_access_token(access_token):
    """Update access token in .env file"""
    try:
        # Load existing .env file
        load_dotenv()

        # Update the access token
        set_key('.env', 'ZERODHA_ACCESS_TOKEN', access_token)

        print(f"✅ Access token updated successfully in .env file")
        return True

    except Exception as e:
        print(f"❌ Error updating access token: {e}")
        return False

def validate_env_config():
    """Validate environment configuration"""
    load_dotenv()

    required_vars = [
        'ZERODHA_API_KEY',
        'ZERODHA_API_SECRET',
        'ZERODHA_ACCESS_TOKEN'
    ]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == 'your_api_key_here' or value == 'your_api_secret_here':
            missing_vars.append(var)

    if missing_vars:
        print("❌ Missing or invalid environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False

    print("✅ Environment configuration is valid")
    return True

def setup_environment():
    """Interactive environment setup"""
    print("🔧 Zerodha Trading Bot Environment Setup")
    print("=" * 50)

    # Check if .env file exists
    if not os.path.exists('.env'):
        print("📄 .env file not found. Creating new one...")
        create_env_file()
    else:
        print("📄 .env file found")

    # Validate configuration
    if not validate_env_config():
        print("\n📝 Please update your .env file with valid API credentials")
        print("   1. Get API credentials from https://kite.zerodha.com/developers/")
        print("   2. Update ZERODHA_API_KEY and ZERODHA_API_SECRET in .env file")
        print("   3. Run 'python zerodha_login.py' to get login URL")
        print("   4. Run 'python generate_token.py' to generate access token")
        return False

    return True

if __name__ == "__main__":
    setup_environment()