"""
Main Trading Bot for SuperTrend + RSI Options Strategy
Orchestrates the entire trading system with proper timing, logging, and error handling
"""

import os
import sys
import time
import signal
import logging
from datetime import datetime, time as dt_time
from typing import Dict, Optional
import pandas as pd
from dotenv import load_dotenv
from kiteconnect import KiteConnect

# Import our modules
from env_setup import validate_env_config
from indicators import TechnicalIndicators, validate_ohlc_data
from strategy import TradingStrategy, SignalType, TradeAction, OptionType, create_strategy_config
from order_manager import OrderManager, create_order_manager
from market_data import MarketDataHandler, create_market_data_handler

# Set up logging
def setup_logging(log_level: str = "INFO", log_file: str = "trading_bot.log"):
    """Setup logging configuration"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return logging.getLogger(__name__)

class SuperTrendTradingBot:
    """
    Main Trading Bot Class

    Implements the complete SuperTrend + RSI options trading strategy:
    - BULLISH: Price > SuperTrend AND RSI > 60 → SELL PUT
    - BEARISH: Price < SuperTrend AND RSI < 45 → SELL CALL
    - Risk Management: 20% stop loss, max 2 trades/day
    - Exit all positions by 3:15 PM
    """

    def __init__(self, config: Dict):
        """
        Initialize Trading Bot

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = setup_logging(
            config.get('LOG_LEVEL', 'INFO'),
            config.get('LOG_FILE', 'trading_bot.log')
        )

        # Initialize components
        self.kite = None
        self.strategy = None
        self.order_manager = None
        self.market_data = None

        # Bot state
        self.is_running = False
        self.trading_mode = config.get('TRADING_MODE', 'TESTING')
        self.underlying = config.get('UNDERLYING', 'NIFTY')
        self.timeframe = config.get('TIMEFRAME', '5minute')

        # Trading hours
        self.trading_start = dt_time(9, 20)  # Avoid first 5 minutes
        self.trading_end = dt_time(15, 15)   # Exit by 3:15 PM

        # Performance tracking
        self.daily_stats = {
            'trades_executed': 0,
            'total_pnl': 0.0,
            'start_time': None,
            'signals_generated': 0,
            'errors_count': 0
        }

        self.logger.info(f"SuperTrend Trading Bot initialized in {self.trading_mode} mode")

    def initialize_connections(self) -> bool:
        """
        Initialize API connections and components

        Returns:
            True if successful, False otherwise
        """
        try:
            # Initialize Zerodha connection
            api_key = self.config.get('ZERODHA_API_KEY')
            access_token = self.config.get('ZERODHA_ACCESS_TOKEN')

            if not api_key or not access_token:
                self.logger.error("Missing API credentials")
                return False

            self.kite = KiteConnect(api_key=api_key)
            self.kite.set_access_token(access_token)

            # Test connection
            if self.trading_mode == "LIVE":
                try:
                    profile = self.kite.profile()
                    self.logger.info(f"Connected to Zerodha - User: {profile.get('user_name', 'Unknown')}")
                except Exception as e:
                    self.logger.error(f"Failed to connect to Zerodha: {e}")
                    return False

            # Initialize components
            strategy_config = create_strategy_config(self.config)
            self.strategy = TradingStrategy(strategy_config)
            self.order_manager = create_order_manager(self.kite, self.trading_mode)
            self.market_data = create_market_data_handler(self.kite, self.trading_mode)

            self.logger.info("All components initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing connections: {e}")
            return False

    def is_trading_time(self) -> bool:
        """Check if current time is within trading hours"""
        current_time = datetime.now().time()
        return self.trading_start <= current_time <= self.trading_end

    def should_exit_positions(self) -> bool:
        """Check if it's time to exit all positions"""
        current_time = datetime.now().time()
        return current_time >= dt_time(15, 15)  # 3:15 PM

    def is_valid_trading_minute(self) -> bool:
        """
        Check if current time is at a valid interval boundary for trading

        Returns:
            True if current time is at interval boundary (e.g., 9:20, 9:25, 9:30)
        """
        current_time = datetime.now()

        # Extract interval from timeframe
        if self.timeframe == "minute":
            interval_minutes = 1
        elif self.timeframe == "5minute":
            interval_minutes = 5
        elif self.timeframe == "10minute":
            interval_minutes = 10
        elif self.timeframe == "15minute":
            interval_minutes = 15
        else:
            interval_minutes = 5  # Default to 5 minutes

        # Check if current minute is at interval boundary
        # For 5-minute: valid at 0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55
        # For 10-minute: valid at 0, 10, 20, 30, 40, 50
        # For 15-minute: valid at 0, 15, 30, 45

        current_minute = current_time.minute
        current_second = current_time.second

        # Check if we're at the exact interval boundary (within 30 seconds)
        is_at_boundary = (current_minute % interval_minutes == 0) and (current_second <= 30)

        if is_at_boundary:
            self.logger.debug(f"✅ Valid trading time: {current_time.strftime('%H:%M:%S')} "
                            f"(interval: {interval_minutes}min)")

        return is_at_boundary

    def wait_for_next_interval(self):
        """
        Wait until the next valid trading interval
        """
        current_time = datetime.now()

        # Extract interval from timeframe
        if self.timeframe == "minute":
            interval_minutes = 1
        elif self.timeframe == "5minute":
            interval_minutes = 5
        elif self.timeframe == "10minute":
            interval_minutes = 10
        elif self.timeframe == "15minute":
            interval_minutes = 15
        else:
            interval_minutes = 5  # Default to 5 minutes

        # Calculate next interval boundary
        current_minute = current_time.minute
        current_second = current_time.second

        # Find next interval boundary
        next_interval_minute = ((current_minute // interval_minutes) + 1) * interval_minutes

        if next_interval_minute >= 60:
            # Move to next hour
            next_time = current_time.replace(hour=current_time.hour + 1, minute=0, second=0, microsecond=0)
        else:
            next_time = current_time.replace(minute=next_interval_minute, second=0, microsecond=0)

        # Calculate wait time
        wait_seconds = (next_time - current_time).total_seconds()

        if wait_seconds > 0:
            self.logger.info(f"⏰ Waiting {wait_seconds:.0f}s until next interval: "
                           f"{next_time.strftime('%H:%M:%S')}")
            time.sleep(wait_seconds)

    def fetch_market_data(self) -> Optional[pd.DataFrame]:
        """
        Fetch latest market data for analysis

        Returns:
            DataFrame with OHLC data or None if failed
        """
        try:
            # Fetch recent data
            df = self.market_data.get_live_data(
                symbol=self.underlying,
                interval=self.timeframe,
                candles=100  # Enough for indicators
            )

            if df.empty:
                self.logger.warning("No market data received")
                return None

            # Validate data quality
            validation = self.market_data.validate_data_quality(df)
            if not validation['valid']:
                self.logger.error(f"Invalid market data: {validation['errors']}")
                return None

            if validation.get('warnings'):
                self.logger.warning(f"Data quality warnings: {validation['warnings']}")

            return df

        except Exception as e:
            self.logger.error(f"Error fetching market data: {e}")
            return None

    def process_trading_signals(self, market_data: pd.DataFrame) -> Dict:
        """
        Process trading signals and execute trades

        Args:
            market_data: OHLC data for analysis

        Returns:
            Processing results
        """
        try:
            current_time = datetime.now()

            # Analyze market condition
            market_analysis = self.strategy.analyze_market_condition(market_data)

            if 'error' in market_analysis:
                self.logger.error(f"Market analysis failed: {market_analysis['error']}")
                return {'status': 'error', 'message': market_analysis['error']}

            # Generate trade signal
            trade_signal = self.strategy.generate_trade_signal(market_analysis, current_time)

            if trade_signal['action'] == TradeAction.NO_ACTION:
                self.logger.debug(f"No action: {trade_signal['reason']}")
                return {'status': 'no_action', 'reason': trade_signal['reason']}

            # Execute trade based on signal
            execution_result = self.execute_trade_signal(trade_signal, market_analysis)

            # Update statistics
            self.daily_stats['signals_generated'] += 1
            if execution_result.get('status') == 'success':
                self.daily_stats['trades_executed'] += 1

            return execution_result

        except Exception as e:
            self.logger.error(f"Error processing trading signals: {e}")
            self.daily_stats['errors_count'] += 1
            return {'status': 'error', 'message': str(e)}

    def execute_trade_signal(self, trade_signal: Dict, market_analysis: Dict) -> Dict:
        """
        Execute trade based on signal

        Args:
            trade_signal: Trade signal information
            market_analysis: Market analysis data

        Returns:
            Execution results
        """
        try:
            action = trade_signal['action']
            current_price = market_analysis.get('current_price', 0)

            if action == TradeAction.SELL_PUT:
                # Calculate PUT strike price
                strike_price = self.strategy.calculate_option_strike(current_price, OptionType.PUT)

                # Sell PUT option
                result = self.order_manager.sell_option(
                    underlying=self.underlying,
                    strike=strike_price,
                    option_type="PE",
                    quantity=self.config.get('POSITION_SIZE', 75)
                )

                if result['status'] == 'success':
                    self.logger.info(f"✅ SELL PUT executed: Strike {strike_price} @ {current_price}")

                    # Update strategy position tracking
                    position_id = result['position_id']
                    self.strategy.update_position_status(position_id, {
                        'entry_premium': result['position_data']['entry_price'],
                        'entry_time': datetime.now(),
                        'signal_type': SignalType.BULLISH
                    })

                return result

            elif action == TradeAction.SELL_CALL:
                # Calculate CALL strike price
                strike_price = self.strategy.calculate_option_strike(current_price, OptionType.CALL)

                # Sell CALL option
                result = self.order_manager.sell_option(
                    underlying=self.underlying,
                    strike=strike_price,
                    option_type="CE",
                    quantity=self.config.get('POSITION_SIZE', 75)
                )

                if result['status'] == 'success':
                    self.logger.info(f"✅ SELL CALL executed: Strike {strike_price} @ {current_price}")

                    # Update strategy position tracking
                    position_id = result['position_id']
                    self.strategy.update_position_status(position_id, {
                        'entry_premium': result['position_data']['entry_price'],
                        'entry_time': datetime.now(),
                        'signal_type': SignalType.BEARISH
                    })

                return result

            elif action == TradeAction.EXIT_POSITION:
                # Exit all positions
                positions_to_exit = trade_signal.get('positions_to_exit', [])

                if not positions_to_exit:
                    # Get all open positions
                    positions_summary = self.order_manager.get_positions_summary()
                    positions_to_exit = [
                        pos['position_id'] for pos in positions_summary.get('open_positions', [])
                    ]

                results = []
                total_pnl = 0.0

                for position_id in positions_to_exit:
                    result = self.order_manager.close_position(position_id)
                    results.append(result)

                    if result['status'] == 'success':
                        pnl = result.get('pnl', 0)
                        total_pnl += pnl
                        self.logger.info(f"✅ Position closed: {position_id} | P&L: ₹{pnl:.2f}")

                        # Remove from strategy tracking
                        self.strategy.remove_position(position_id)

                self.daily_stats['total_pnl'] += total_pnl

                return {
                    'status': 'success',
                    'action': 'exit_positions',
                    'positions_closed': len(results),
                    'total_pnl': total_pnl,
                    'results': results
                }

            return {'status': 'error', 'message': f'Unknown action: {action}'}

        except Exception as e:
            self.logger.error(f"Error executing trade signal: {e}")
            return {'status': 'error', 'message': str(e)}

    def check_risk_management(self) -> Dict:
        """
        Check risk management rules and stop losses

        Returns:
            Risk management results
        """
        try:
            # Check order manager risk limits
            risk_check = self.order_manager.check_risk_limits()

            actions_taken = []

            if risk_check['status'] == 'success' and risk_check['risk_alerts']:
                for alert in risk_check['risk_alerts']:
                    if alert['alert_type'] == 'STOP_LOSS':
                        position_id = alert['position_id']

                        # Close position due to stop loss
                        close_result = self.order_manager.close_position(position_id)

                        if close_result['status'] == 'success':
                            self.logger.warning(f"🛑 STOP LOSS: Closed position {position_id} | "
                                              f"P&L: ₹{close_result.get('pnl', 0):.2f}")

                            # Remove from strategy tracking
                            self.strategy.remove_position(position_id)

                            actions_taken.append({
                                'action': 'stop_loss_exit',
                                'position_id': position_id,
                                'pnl': close_result.get('pnl', 0)
                            })

            return {
                'status': 'success',
                'risk_alerts': risk_check.get('risk_alerts', []),
                'actions_taken': actions_taken,
                'daily_pnl': risk_check.get('daily_pnl', 0)
            }

        except Exception as e:
            self.logger.error(f"Error in risk management check: {e}")
            return {'status': 'error', 'message': str(e)}

    def run_trading_cycle(self) -> Dict:
        """
        Run one complete trading cycle

        Returns:
            Cycle results
        """
        try:
            cycle_start = datetime.now()

            # Check if it's trading time
            if not self.is_trading_time():
                return {'status': 'outside_trading_hours', 'time': cycle_start}

            # Check if we should exit all positions (always check at 3:15 PM)
            if self.should_exit_positions():
                exit_result = self.order_manager.close_all_positions()
                self.logger.info(f"🕒 End of day: Closed all positions | "
                               f"Total P&L: ₹{exit_result.get('total_pnl', 0):.2f}")
                return {'status': 'end_of_day_exit', 'result': exit_result}

            # Check if current time is at valid interval boundary for new trades
            if not self.is_valid_trading_minute():
                return {
                    'status': 'waiting_for_interval',
                    'time': cycle_start,
                    'message': f'Waiting for next {self.timeframe} interval boundary'
                }

            # Fetch market data
            market_data = self.fetch_market_data()
            if market_data is None:
                return {'status': 'data_error', 'message': 'Failed to fetch market data'}

            # Check risk management first
            risk_result = self.check_risk_management()
            if risk_result.get('actions_taken'):
                self.logger.info(f"Risk management actions taken: {len(risk_result['actions_taken'])}")

            # Process trading signals
            signal_result = self.process_trading_signals(market_data)

            # Get current status
            strategy_status = self.strategy.get_strategy_status()
            positions_summary = self.order_manager.get_positions_summary()

            cycle_end = datetime.now()
            cycle_duration = (cycle_end - cycle_start).total_seconds()

            return {
                'status': 'completed',
                'cycle_duration': cycle_duration,
                'signal_result': signal_result,
                'risk_result': risk_result,
                'strategy_status': strategy_status,
                'positions_summary': positions_summary,
                'timestamp': cycle_end
            }

        except Exception as e:
            self.logger.error(f"Error in trading cycle: {e}")
            self.daily_stats['errors_count'] += 1
            return {'status': 'error', 'message': str(e)}

    def start_trading(self):
        """Start the trading bot main loop"""
        try:
            self.logger.info("🚀 Starting SuperTrend Trading Bot...")

            # Validate environment
            if not validate_env_config():
                self.logger.error("Environment validation failed")
                return

            # Initialize connections
            if not self.initialize_connections():
                self.logger.error("Failed to initialize connections")
                return

            # Reset daily counters
            self.strategy.reset_daily_counters()
            self.order_manager.reset_daily_data()
            self.daily_stats['start_time'] = datetime.now()

            self.is_running = True
            self.logger.info(f"✅ Trading bot started in {self.trading_mode} mode")
            self.logger.info(f"📊 Strategy: SuperTrend({self.strategy.supertrend_period}, "
                           f"{self.strategy.supertrend_multiplier}) + RSI({self.strategy.rsi_period})")
            self.logger.info(f"⏰ Trading hours: {self.trading_start} - {self.trading_end}")

            # Main trading loop
            while self.is_running:
                try:
                    cycle_result = self.run_trading_cycle()

                    # Log cycle summary
                    if cycle_result['status'] == 'completed':
                        signal_status = cycle_result['signal_result'].get('status', 'unknown')
                        positions_count = cycle_result['positions_summary']['summary']['open_count']
                        daily_pnl = cycle_result['positions_summary']['daily_pnl']

                        self.logger.info(f"📊 Cycle completed: {signal_status} | Positions: {positions_count} | "
                                       f"Daily P&L: ₹{daily_pnl:.2f}")

                    elif cycle_result['status'] == 'end_of_day_exit':
                        self.logger.info("📈 Trading day completed")
                        break

                    elif cycle_result['status'] == 'waiting_for_interval':
                        # Wait for next valid interval boundary
                        self.wait_for_next_interval()
                        continue

                    elif cycle_result['status'] == 'outside_trading_hours':
                        self.logger.debug("Outside trading hours, waiting...")
                        time.sleep(60)  # Check every minute
                        continue

                    # For other statuses, wait a short time before next cycle
                    time.sleep(30)  # 30 seconds between cycles

                except KeyboardInterrupt:
                    self.logger.info("Received interrupt signal, stopping...")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main loop: {e}")
                    time.sleep(60)  # Wait before retrying

            # Final cleanup
            self.stop_trading()

        except Exception as e:
            self.logger.error(f"Critical error in trading bot: {e}")
            self.stop_trading()

    def stop_trading(self):
        """Stop the trading bot and cleanup"""
        try:
            self.is_running = False

            # Close all open positions
            if self.order_manager:
                positions_summary = self.order_manager.get_positions_summary()
                if positions_summary['summary']['open_count'] > 0:
                    self.logger.info("Closing all open positions...")
                    close_result = self.order_manager.close_all_positions()
                    self.logger.info(f"Final P&L: ₹{close_result.get('total_pnl', 0):.2f}")

            # Print daily summary
            self.print_daily_summary()

            self.logger.info("🛑 Trading bot stopped")

        except Exception as e:
            self.logger.error(f"Error stopping trading bot: {e}")

    def print_daily_summary(self):
        """Print daily trading summary"""
        try:
            if self.daily_stats['start_time']:
                duration = datetime.now() - self.daily_stats['start_time']

                self.logger.info("=" * 60)
                self.logger.info("📊 DAILY TRADING SUMMARY")
                self.logger.info("=" * 60)
                self.logger.info(f"Trading Duration: {duration}")
                self.logger.info(f"Signals Generated: {self.daily_stats['signals_generated']}")
                self.logger.info(f"Trades Executed: {self.daily_stats['trades_executed']}")
                self.logger.info(f"Total P&L: ₹{self.daily_stats['total_pnl']:.2f}")
                self.logger.info(f"Errors Count: {self.daily_stats['errors_count']}")

                if self.order_manager:
                    positions_summary = self.order_manager.get_positions_summary()
                    self.logger.info(f"Final Daily P&L: ₹{positions_summary['daily_pnl']:.2f}")

                self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"Error printing daily summary: {e}")

def signal_handler(signum, frame):
    """Handle interrupt signals"""
    print("\nReceived interrupt signal. Stopping trading bot...")
    sys.exit(0)

def main():
    """Main function to run the trading bot"""
    try:
        # Load environment variables
        load_dotenv()

        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Create configuration from environment
        config = {
            'ZERODHA_API_KEY': os.getenv('ZERODHA_API_KEY'),
            'ZERODHA_ACCESS_TOKEN': os.getenv('ZERODHA_ACCESS_TOKEN'),
            'TRADING_MODE': os.getenv('TRADING_MODE', 'TESTING'),
            'TIMEFRAME': os.getenv('TIMEFRAME', '5minute'),
            'UNDERLYING': 'NIFTY',
            'SUPERTREND_PERIOD': int(os.getenv('SUPERTREND_PERIOD', 5)),
            'SUPERTREND_MULTIPLIER': float(os.getenv('SUPERTREND_MULTIPLIER', 3.0)),
            'RSI_PERIOD': int(os.getenv('RSI_PERIOD', 14)),
            'RSI_BUY_THRESHOLD': float(os.getenv('RSI_BUY_THRESHOLD', 60)),
            'RSI_SELL_THRESHOLD': float(os.getenv('RSI_SELL_THRESHOLD', 45)),
            'MAX_TRADES_PER_DAY': int(os.getenv('MAX_TRADES_PER_DAY', 2)),
            'STOP_LOSS_PERCENTAGE': float(os.getenv('STOP_LOSS_PERCENTAGE', 20)),
            'POSITION_SIZE': int(os.getenv('POSITION_SIZE', 75)),
            'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
            'LOG_FILE': os.getenv('LOG_FILE', 'trading_bot.log')
        }

        # Create and start trading bot
        bot = SuperTrendTradingBot(config)
        bot.start_trading()

    except Exception as e:
        print(f"Failed to start trading bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()