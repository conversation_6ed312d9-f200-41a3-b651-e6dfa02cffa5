# SuperTrend + RSI Options Trading Bot

An automated intraday options trading system that implements a SuperTrend and RSI-based strategy for selling options on Zerodha.

## 🔽 Strategy Logic

### Market Entry Conditions
- **Market Open**: After 9:20 AM (avoids first 5 minutes)
- **Data Timeframe**: 5-minute candles
- **Maximum Trades**: 2 per day

### Trading Signals

#### 🟢 BULLISH CASE (Sell PUT Options)
- ✅ Price > SuperTrend
- ✅ RSI > 60
- **Action**: SELL ATM or Slightly OTM Put (PE) - 1 lot

#### 🔴 BEARISH CASE (Sell CALL Options)
- ✅ Price < SuperTrend
- ✅ RSI < 45
- **Action**: SELL ATM or Slightly OTM Call (CE) - 1 lot

### Risk Management
- **Stop Loss**: 20% of premium OR SuperTrend flip (whichever comes first)
- **Daily Exit**: All positions closed by 3:15 PM
- **Position Size**: Configurable (default: 75 quantity)
- **Max Trades**: 2 trades per day
- **Supported Instruments**: Any FNO instrument (NIFTY, BANKNIFTY, FINNIFTY, etc.)

## 📁 Project Structure

```
algotrade-intraday-supertrend/
├── trading_bot.py          # Main trading bot orchestrator
├── strategy.py             # Trading strategy implementation
├── indicators.py           # SuperTrend and RSI calculations
├── order_manager.py        # Order placement and position management
├── market_data.py          # Real-time data fetching and caching
├── env_setup.py           # Environment configuration utility
├── zerodha_login.py       # Zerodha authentication helper
├── generate_token.py      # Access token generation
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables (API keys, config)
├── .env.example          # Example environment configuration
└── README.md             # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd algotrade-intraday-supertrend

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
python env_setup.py
```

### 2. Zerodha API Setup

1. **Get API Credentials**:
   - Visit [Kite Connect](https://kite.zerodha.com/developers/)
   - Create a new app and get API Key & Secret

2. **Update .env file**:
   ```bash
   ZERODHA_API_KEY=your_api_key_here
   ZERODHA_API_SECRET=your_api_secret_here
   ```

3. **Generate Access Token**:
   ```bash
   # Get login URL
   python zerodha_login.py

   # Follow the URL, login, and copy request_token
   # Then generate access token
   python generate_token.py
   ```

### 3. Configuration

Edit `.env` file to customize strategy parameters:

```bash
# Trading Configuration
TIMEFRAME=5minute

# Instrument Configuration (Any FNO instrument)
INSTRUMENT=NIFTY  # NIFTY, BANKNIFTY, FINNIFTY, MIDCPNIFTY, etc.

SUPERTREND_PERIOD=5
SUPERTREND_MULTIPLIER=3
RSI_PERIOD=14
RSI_BUY_THRESHOLD=60
RSI_SELL_THRESHOLD=45

# Risk Management
MAX_TRADES_PER_DAY=2
STOP_LOSS_PERCENTAGE=20
POSITION_SIZE=75

# Trading Mode
TRADING_MODE=TESTING  # or LIVE
```

### 4. Run the Bot

```bash
# Test mode (simulated trading)
python trading_bot.py

# For live trading, set TRADING_MODE=LIVE in .env
```

## ⚙️ Configuration Options

### Instrument Configuration
- `INSTRUMENT`: Any FNO instrument (default: NIFTY)
  - **Supported**: NIFTY, BANKNIFTY, FINNIFTY, MIDCPNIFTY, SENSEX, BANKEX
  - **Strike Intervals**: Auto-detected (NIFTY: 50, BANKNIFTY: 100, FINNIFTY: 50, etc.)
  - **Examples**: Set `INSTRUMENT=BANKNIFTY` for Bank Nifty trading

### Strategy Parameters
- `SUPERTREND_PERIOD`: ATR period for SuperTrend (default: 5)
- `SUPERTREND_MULTIPLIER`: ATR multiplier (default: 3.0)
- `RSI_PERIOD`: RSI calculation period (default: 14)
- `RSI_BUY_THRESHOLD`: RSI level for bullish signals (default: 60)
- `RSI_SELL_THRESHOLD`: RSI level for bearish signals (default: 45)

### Risk Management
- `MAX_TRADES_PER_DAY`: Maximum trades per day (default: 2)
- `STOP_LOSS_PERCENTAGE`: Stop loss percentage (default: 20%)
- `POSITION_SIZE`: Option quantity per trade (default: 75)

### Trading Hours
- `TRADING_START_TIME`: Start time (default: 09:15)
- `TRADING_END_TIME`: End time (default: 15:15)

## 🔧 Components Overview

### 1. Technical Indicators (`indicators.py`)
- **SuperTrend**: Trend-following indicator using ATR
- **RSI**: Relative Strength Index for momentum
- **Data Validation**: OHLC data quality checks

### 2. Trading Strategy (`strategy.py`)
- **Signal Generation**: Combines SuperTrend and RSI
- **Market Analysis**: Real-time condition assessment
- **Risk Rules**: Stop loss and position limits

### 3. Order Management (`order_manager.py`)
- **Option Orders**: Automated option selling
- **Position Tracking**: Real-time P&L monitoring
- **Risk Monitoring**: Stop loss enforcement

### 4. Market Data (`market_data.py`)
- **Live Data**: Real-time OHLC fetching
- **Data Caching**: Efficient data management
- **Quality Validation**: Data integrity checks

### 5. Main Bot (`trading_bot.py`)
- **Orchestration**: Coordinates all components
- **Timing Control**: Trading hours management
- **Error Handling**: Robust error recovery
- **Logging**: Comprehensive activity logging

## 📊 Monitoring & Logging

The bot provides comprehensive logging:

```bash
# View real-time logs
tail -f trading_bot.log

# Key log events:
# - Signal generation
# - Order placement
# - Position updates
# - Risk alerts
# - Daily summary
```

## 🧪 Testing Mode

The bot includes a testing mode for safe strategy validation:

- **Simulated Orders**: No real money at risk
- **Mock Data**: Realistic market data simulation
- **Full Strategy Testing**: Complete workflow validation

Set `TRADING_MODE=TESTING` in `.env` for testing.

## ⚠️ Risk Warnings

1. **Market Risk**: Options trading involves significant risk
2. **System Risk**: Ensure stable internet and system uptime
3. **API Limits**: Respect Zerodha API rate limits
4. **Testing First**: Always test thoroughly before live trading
5. **Position Monitoring**: Monitor positions actively during market hours

## 🔒 Security Best Practices

1. **API Keys**: Never commit API keys to version control
2. **Access Tokens**: Regenerate tokens regularly
3. **Environment**: Use `.env` for sensitive configuration
4. **Permissions**: Limit API permissions to required functions

## 📈 Performance Tracking

The bot tracks:
- Daily P&L
- Number of trades executed
- Signal accuracy
- Risk events
- System errors

## 🛠️ Troubleshooting

### Common Issues

1. **API Connection Failed**:
   - Check API credentials in `.env`
   - Verify access token is valid
   - Ensure internet connectivity

2. **No Market Data**:
   - Check trading hours
   - Verify instrument symbols
   - Check API permissions

3. **Orders Not Placing**:
   - Verify trading mode setting
   - Check account balance
   - Ensure market is open

### Debug Mode

Enable debug logging:
```bash
LOG_LEVEL=DEBUG
```

## 📞 Support

For issues and questions:
1. Check logs for error details
2. Verify configuration settings
3. Test in TESTING mode first
4. Review Zerodha API documentation

## 📄 License

This project is for educational purposes. Use at your own risk.

---

**Disclaimer**: This software is for educational purposes only. Trading involves risk and you should carefully consider your investment objectives and risk tolerance before trading. Past performance is not indicative of future results.