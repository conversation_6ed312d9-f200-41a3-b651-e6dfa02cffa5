"""
Trading Strategy Engine for SuperTrend + RSI Options Strategy
Implements the core trading logic for selling options based on market signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from typing import Dict, List, Optional, Tuple
import logging
from enum import Enum

from indicators import TechnicalIndicators, validate_ohlc_data

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalType(Enum):
    """Enumeration for signal types"""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    NEUTRAL = "NEUTRAL"

class OptionType(Enum):
    """Enumeration for option types"""
    CALL = "CE"
    PUT = "PE"

class TradeAction(Enum):
    """Enumeration for trade actions"""
    SELL_PUT = "SELL_PUT"
    SELL_CALL = "SELL_CALL"
    EXIT_POSITION = "EXIT_POSITION"
    NO_ACTION = "NO_ACTION"

class TradingStrategy:
    """
    SuperTrend + RSI Options Trading Strategy

    Strategy Logic:
    - BULLISH: Price > SuperTrend AND RSI > 60 → SELL PUT (ATM/OTM)
    - BEARISH: Price < SuperTrend AND RSI < 45 → SELL CALL (ATM/OTM)
    - Exit all positions by 3:15 PM
    - Stop Loss: 20% of premium or SuperTrend flip
    """

    def __init__(self, config: Dict):
        """
        Initialize trading strategy

        Args:
            config: Configuration dictionary with strategy parameters
        """
        self.config = config
        self.indicators = TechnicalIndicators()
        self.logger = logger

        # Strategy parameters
        self.supertrend_period = config.get('supertrend_period', 5)
        self.supertrend_multiplier = config.get('supertrend_multiplier', 3.0)
        self.rsi_period = config.get('rsi_period', 14)
        self.rsi_buy_threshold = config.get('rsi_buy_threshold', 60)
        self.rsi_sell_threshold = config.get('rsi_sell_threshold', 45)

        # Risk management
        self.max_trades_per_day = config.get('max_trades_per_day', 2)
        self.stop_loss_percentage = config.get('stop_loss_percentage', 20)
        self.position_size = config.get('position_size', 75)

        # Trading hours
        self.trading_start = time(9, 20)  # Avoid first 5 minutes
        self.trading_end = time(15, 15)   # Exit by 3:15 PM

        # State tracking
        self.daily_trade_count = 0
        self.current_positions = {}
        self.last_signal = SignalType.NEUTRAL
        self.last_supertrend_direction = 0

    def is_trading_time(self, current_time: datetime) -> bool:
        """
        Check if current time is within trading hours

        Args:
            current_time: Current datetime

        Returns:
            True if within trading hours, False otherwise
        """
        current_time_only = current_time.time()
        return self.trading_start <= current_time_only <= self.trading_end

    def should_avoid_first_minutes(self, current_time: datetime) -> bool:
        """
        Check if we should avoid trading in first 5 minutes after market open

        Args:
            current_time: Current datetime

        Returns:
            True if should avoid, False otherwise
        """
        market_open = time(9, 15)
        avoid_until = time(9, 20)
        current_time_only = current_time.time()

        return market_open <= current_time_only < avoid_until

    def analyze_market_condition(self, data: pd.DataFrame) -> Dict:
        """
        Analyze current market condition using SuperTrend and RSI

        Args:
            data: OHLC data with latest candles

        Returns:
            Dictionary with market analysis results
        """
        try:
            # Validate data
            if not validate_ohlc_data(data):
                return {'signal': SignalType.NEUTRAL, 'error': 'Invalid OHLC data'}

            # Get signals from indicators
            signals_df = self.indicators.get_trading_signals(
                data,
                self.supertrend_period,
                self.supertrend_multiplier,
                self.rsi_period,
                self.rsi_buy_threshold,
                self.rsi_sell_threshold
            )

            if signals_df.empty:
                return {'signal': SignalType.NEUTRAL, 'error': 'Failed to calculate indicators'}

            # Get latest values
            latest = signals_df.iloc[-1]
            current_price = latest['close']
            supertrend_value = latest['supertrend']
            rsi_value = latest['rsi']
            bullish_signal = latest['bullish_signal']
            bearish_signal = latest['bearish_signal']

            # Determine signal type
            if bullish_signal:
                signal_type = SignalType.BULLISH
            elif bearish_signal:
                signal_type = SignalType.BEARISH
            else:
                signal_type = SignalType.NEUTRAL

            # Check for SuperTrend flip (for stop loss)
            supertrend_flip = False
            if len(signals_df) > 1:
                prev_direction = signals_df.iloc[-2]['st_direction']
                curr_direction = latest['st_direction']
                supertrend_flip = prev_direction != curr_direction

            analysis = {
                'signal': signal_type,
                'current_price': current_price,
                'supertrend': supertrend_value,
                'rsi': rsi_value,
                'supertrend_flip': supertrend_flip,
                'price_above_supertrend': current_price > supertrend_value,
                'signal_strength': latest['signal_strength'],
                'timestamp': datetime.now()
            }

            self.logger.info(f"Market Analysis: {signal_type.value} | Price: {current_price:.2f} | "
                           f"SuperTrend: {supertrend_value:.2f} | RSI: {rsi_value:.2f}")

            return analysis

        except Exception as e:
            self.logger.error(f"Error analyzing market condition: {e}")
            return {'signal': SignalType.NEUTRAL, 'error': str(e)}

    def generate_trade_signal(self, market_analysis: Dict, current_time: datetime) -> Dict:
        """
        Generate trade signal based on market analysis and strategy rules

        Args:
            market_analysis: Market condition analysis
            current_time: Current datetime

        Returns:
            Dictionary with trade signal information
        """
        try:
            # Check trading time constraints
            if not self.is_trading_time(current_time):
                return {
                    'action': TradeAction.NO_ACTION,
                    'reason': 'Outside trading hours'
                }

            if self.should_avoid_first_minutes(current_time):
                return {
                    'action': TradeAction.NO_ACTION,
                    'reason': 'Avoiding first 5 minutes after market open'
                }

            # Check daily trade limit
            if self.daily_trade_count >= self.max_trades_per_day:
                return {
                    'action': TradeAction.NO_ACTION,
                    'reason': f'Daily trade limit reached ({self.max_trades_per_day})'
                }

            # Check for exit conditions first
            if current_time.time() >= time(15, 15):
                if self.current_positions:
                    return {
                        'action': TradeAction.EXIT_POSITION,
                        'reason': 'End of day exit (3:15 PM)',
                        'positions_to_exit': list(self.current_positions.keys())
                    }

            # Check for stop loss conditions
            if self.current_positions and market_analysis.get('supertrend_flip', False):
                return {
                    'action': TradeAction.EXIT_POSITION,
                    'reason': 'SuperTrend flip - Stop loss triggered',
                    'positions_to_exit': list(self.current_positions.keys())
                }

            # Generate new trade signals
            signal = market_analysis.get('signal', SignalType.NEUTRAL)

            if signal == SignalType.BULLISH and not self.current_positions:
                return {
                    'action': TradeAction.SELL_PUT,
                    'option_type': OptionType.PUT,
                    'reason': 'Bullish signal: Price > SuperTrend AND RSI > 60',
                    'current_price': market_analysis.get('current_price'),
                    'rsi': market_analysis.get('rsi'),
                    'supertrend': market_analysis.get('supertrend')
                }

            elif signal == SignalType.BEARISH and not self.current_positions:
                return {
                    'action': TradeAction.SELL_CALL,
                    'option_type': OptionType.CALL,
                    'reason': 'Bearish signal: Price < SuperTrend AND RSI < 45',
                    'current_price': market_analysis.get('current_price'),
                    'rsi': market_analysis.get('rsi'),
                    'supertrend': market_analysis.get('supertrend')
                }

            return {
                'action': TradeAction.NO_ACTION,
                'reason': 'No valid signal or position already exists'
            }

        except Exception as e:
            self.logger.error(f"Error generating trade signal: {e}")
            return {
                'action': TradeAction.NO_ACTION,
                'reason': f'Error: {str(e)}'
            }

    def calculate_option_strike(self, current_price: float, option_type: OptionType,
                              instrument: str = "NIFTY") -> float:
        """
        Calculate appropriate option strike price (ATM or slightly OTM)

        Args:
            current_price: Current underlying price
            option_type: Type of option (CALL or PUT)
            instrument: Underlying instrument name

        Returns:
            Calculated strike price
        """
        try:
            # Get strike interval based on instrument
            strike_intervals = {
                'NIFTY': 50,
                'BANKNIFTY': 100,
                'FINNIFTY': 50,
                'MIDCPNIFTY': 25,
                'SENSEX': 100,
                'BANKEX': 100
            }

            strike_interval = strike_intervals.get(instrument.upper(), 50)

            if option_type == OptionType.PUT:
                # For PUT: ATM or slightly OTM (lower strike)
                atm_strike = round(current_price / strike_interval) * strike_interval
                otm_strike = atm_strike - strike_interval
                return otm_strike  # Slightly OTM PUT

            elif option_type == OptionType.CALL:
                # For CALL: ATM or slightly OTM (higher strike)
                atm_strike = round(current_price / strike_interval) * strike_interval
                otm_strike = atm_strike + strike_interval
                return otm_strike  # Slightly OTM CALL

            return current_price

        except Exception as e:
            self.logger.error(f"Error calculating option strike: {e}")
            return current_price

    def update_position_status(self, position_id: str, status: Dict):
        """
        Update position status for tracking

        Args:
            position_id: Unique position identifier
            status: Position status dictionary
        """
        try:
            if position_id in self.current_positions:
                self.current_positions[position_id].update(status)
            else:
                self.current_positions[position_id] = status

            self.logger.info(f"Updated position {position_id}: {status}")

        except Exception as e:
            self.logger.error(f"Error updating position status: {e}")

    def remove_position(self, position_id: str):
        """
        Remove position from tracking

        Args:
            position_id: Position identifier to remove
        """
        try:
            if position_id in self.current_positions:
                del self.current_positions[position_id]
                self.logger.info(f"Removed position: {position_id}")

        except Exception as e:
            self.logger.error(f"Error removing position: {e}")

    def check_stop_loss(self, position_id: str, current_premium: float) -> bool:
        """
        Check if stop loss should be triggered for a position

        Args:
            position_id: Position identifier
            current_premium: Current option premium

        Returns:
            True if stop loss should be triggered, False otherwise
        """
        try:
            if position_id not in self.current_positions:
                return False

            position = self.current_positions[position_id]
            entry_premium = position.get('entry_premium', 0)

            if entry_premium <= 0:
                return False

            # Calculate current P&L percentage
            pnl_percentage = ((current_premium - entry_premium) / entry_premium) * 100

            # For short options, loss occurs when premium increases
            # Stop loss triggers when loss exceeds threshold
            if pnl_percentage > self.stop_loss_percentage:
                self.logger.warning(f"Stop loss triggered for {position_id}: "
                                  f"P&L: {pnl_percentage:.2f}% (Threshold: {self.stop_loss_percentage}%)")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking stop loss: {e}")
            return False

    def reset_daily_counters(self):
        """Reset daily trade counters (call at start of new trading day)"""
        self.daily_trade_count = 0
        self.current_positions.clear()
        self.last_signal = SignalType.NEUTRAL
        self.logger.info("Daily counters reset")

    def get_strategy_status(self) -> Dict:
        """
        Get current strategy status

        Returns:
            Dictionary with strategy status information
        """
        return {
            'daily_trade_count': self.daily_trade_count,
            'max_trades_per_day': self.max_trades_per_day,
            'current_positions': len(self.current_positions),
            'position_details': self.current_positions.copy(),
            'last_signal': self.last_signal.value,
            'strategy_config': {
                'supertrend_period': self.supertrend_period,
                'supertrend_multiplier': self.supertrend_multiplier,
                'rsi_period': self.rsi_period,
                'rsi_buy_threshold': self.rsi_buy_threshold,
                'rsi_sell_threshold': self.rsi_sell_threshold,
                'stop_loss_percentage': self.stop_loss_percentage,
                'position_size': self.position_size
            }
        }

def create_strategy_config(env_config: Dict) -> Dict:
    """
    Create strategy configuration from environment variables

    Args:
        env_config: Environment configuration dictionary

    Returns:
        Strategy configuration dictionary
    """
    return {
        'supertrend_period': int(env_config.get('SUPERTREND_PERIOD', 5)),
        'supertrend_multiplier': float(env_config.get('SUPERTREND_MULTIPLIER', 3.0)),
        'rsi_period': int(env_config.get('RSI_PERIOD', 14)),
        'rsi_buy_threshold': float(env_config.get('RSI_BUY_THRESHOLD', 60)),
        'rsi_sell_threshold': float(env_config.get('RSI_SELL_THRESHOLD', 45)),
        'max_trades_per_day': int(env_config.get('MAX_TRADES_PER_DAY', 2)),
        'stop_loss_percentage': float(env_config.get('STOP_LOSS_PERCENTAGE', 20)),
        'position_size': int(env_config.get('POSITION_SIZE', 75))
    }