"""
Backtesting Simulator for SuperTrend + RSI Strategy
Simulates a full trading day using real historical data
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time as dt_time
from typing import Dict, List, Optional
import logging
from dotenv import load_dotenv
from kiteconnect import KiteConnect

# Import our modules
from indicators import TechnicalIndicators, validate_ohlc_data
from strategy import TradingStrategy, SignalType, TradeAction, OptionType, create_strategy_config
from order_manager import OrderManager, create_order_manager
from market_data import MarketDataHandler, create_market_data_handler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BacktestSimulator:
    """
    Simulates a full trading day using historical data
    """

    def __init__(self, config: Dict):
        """
        Initialize backtesting simulator

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logger

        # Initialize components
        self.kite = None
        self.strategy = None
        self.order_manager = None
        self.market_data = None

        # Simulation parameters
        self.instrument = config.get('INSTRUMENT', 'NIFTY')
        self.timeframe = config.get('TIMEFRAME', '5minute')
        self.simulation_date = None

        # Results tracking
        self.simulation_results = {
            'trades': [],
            'signals': [],
            'pnl_history': [],
            'daily_stats': {},
            'timeline': []
        }

        self.logger.info(f"Backtest Simulator initialized for {self.instrument}")

    def initialize_connections(self) -> bool:
        """Initialize API connections for historical data"""
        try:
            # Initialize Zerodha connection for historical data
            api_key = self.config.get('ZERODHA_API_KEY')
            access_token = self.config.get('ZERODHA_ACCESS_TOKEN')

            if api_key and access_token:
                self.kite = KiteConnect(api_key=api_key)
                self.kite.set_access_token(access_token)

                # Test connection
                try:
                    profile = self.kite.profile()
                    self.logger.info(f"Connected to Zerodha for historical data")
                except Exception as e:
                    self.logger.warning(f"Zerodha connection failed, using mock data: {e}")
                    self.kite = None

            # Initialize components in TESTING mode for simulation
            strategy_config = create_strategy_config(self.config)
            self.strategy = TradingStrategy(strategy_config)
            self.order_manager = create_order_manager(self.kite, "TESTING", strategy_config)
            self.market_data = create_market_data_handler(self.kite, "TESTING")

            self.logger.info("All simulation components initialized")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing connections: {e}")
            return False

    def get_historical_data_for_date(self, date: datetime) -> pd.DataFrame:
        """
        Get historical data for a specific trading date

        Args:
            date: Trading date to simulate

        Returns:
            DataFrame with intraday OHLC data
        """
        try:
            # Set time range for the trading day
            from_date = date.replace(hour=9, minute=0, second=0, microsecond=0)
            to_date = date.replace(hour=15, minute=30, second=0, microsecond=0)

            self.logger.info(f"Fetching historical data for {date.strftime('%Y-%m-%d')}")

            # Get historical data
            df = self.market_data.get_historical_data(
                instrument_token=self.market_data._get_instrument_token(self.instrument),
                from_date=from_date,
                to_date=to_date,
                interval=self.timeframe
            )

            if df.empty:
                self.logger.error(f"No historical data available for {date.strftime('%Y-%m-%d')}")
                return pd.DataFrame()

            # Filter to trading hours only (9:15 AM to 3:30 PM)
            trading_start = dt_time(9, 15)
            trading_end = dt_time(15, 30)

            df = df.between_time(trading_start, trading_end)

            self.logger.info(f"Loaded {len(df)} candles for simulation")
            return df

        except Exception as e:
            self.logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()

    def simulate_option_pricing(self, underlying_price: float, strike: float,
                              option_type: str, time_to_expiry: float = 7) -> float:
        """
        Simulate option pricing using simplified Black-Scholes approximation

        Args:
            underlying_price: Current underlying price
            strike: Strike price
            option_type: "CE" or "PE"
            time_to_expiry: Days to expiry

        Returns:
            Estimated option premium
        """
        try:
            # Simplified option pricing for simulation
            # This is a rough approximation for backtesting purposes

            volatility = 0.20  # Assumed 20% volatility
            risk_free_rate = 0.06  # 6% risk-free rate

            # Calculate intrinsic value
            if option_type == "CE":
                intrinsic = max(0, underlying_price - strike)
            else:  # PE
                intrinsic = max(0, strike - underlying_price)

            # Calculate time value (simplified)
            time_value = (volatility * underlying_price * np.sqrt(time_to_expiry / 365)) / 4

            # Add some randomness for realism
            noise = np.random.normal(0, time_value * 0.1)

            premium = intrinsic + time_value + noise
            return max(premium, 1.0)  # Minimum premium of ₹1

        except Exception as e:
            self.logger.error(f"Error simulating option price: {e}")
            return 50.0  # Default premium

    def _is_valid_simulation_time(self, current_time: datetime) -> bool:
        """Check if current time is valid for signal generation"""
        # Extract interval from timeframe
        if self.timeframe == "minute":
            interval_minutes = 1
        elif self.timeframe == "5minute":
            interval_minutes = 5
        elif self.timeframe == "10minute":
            interval_minutes = 10
        elif self.timeframe == "15minute":
            interval_minutes = 15
        else:
            interval_minutes = 5

        # Check if current minute is at interval boundary
        return current_time.minute % interval_minutes == 0

    def _log_market_state(self, current_time: datetime, market_analysis: Dict, trade_signal: Dict):
        """Log current market state for debugging"""
        signal_type = market_analysis.get('signal', SignalType.NEUTRAL)
        current_price = market_analysis.get('current_price', 0)
        supertrend = market_analysis.get('supertrend', 0)
        rsi = market_analysis.get('rsi', 0)
        action = trade_signal['action']

        if action != TradeAction.NO_ACTION:
            self.logger.info(f"🎯 {current_time.strftime('%H:%M')} | {signal_type.value} | "
                           f"Price: ₹{current_price:.1f} | ST: ₹{supertrend:.1f} | "
                           f"RSI: {rsi:.1f} | Action: {action.value}")

    def _simulate_trade_execution(self, trade_signal: Dict, market_analysis: Dict,
                                current_time: datetime) -> Dict:
        """Simulate trade execution"""
        try:
            action = trade_signal['action']
            current_price = market_analysis.get('current_price', 0)

            if action == TradeAction.SELL_PUT:
                # Calculate PUT strike and simulate option premium
                strike_price = self.strategy.calculate_option_strike(
                    current_price, OptionType.PUT, self.instrument
                )
                option_premium = self.simulate_option_pricing(current_price, strike_price, "PE")

                # Record trade
                trade_record = {
                    'time': current_time.strftime('%H:%M'),
                    'action': 'SELL_PUT',
                    'underlying_price': current_price,
                    'strike': strike_price,
                    'premium': option_premium,
                    'signal_type': 'BULLISH',
                    'rsi': market_analysis.get('rsi', 0),
                    'supertrend': market_analysis.get('supertrend', 0)
                }

                self.simulation_results['trades'].append(trade_record)

                # Simulate order execution
                result = self.order_manager.sell_option(
                    underlying=self.instrument,
                    strike=strike_price,
                    option_type="PE",
                    quantity=self.config.get('POSITION_SIZE', 75)
                )

                if result['status'] == 'success':
                    # Override the simulated premium
                    position_id = result['position_id']
                    self.order_manager.positions[position_id]['entry_price'] = option_premium

                    self.logger.info(f"✅ SELL PUT executed: Strike ₹{strike_price} @ ₹{option_premium:.1f}")

                return result

            elif action == TradeAction.SELL_CALL:
                # Calculate CALL strike and simulate option premium
                strike_price = self.strategy.calculate_option_strike(
                    current_price, OptionType.CALL, self.instrument
                )
                option_premium = self.simulate_option_pricing(current_price, strike_price, "CE")

                # Record trade
                trade_record = {
                    'time': current_time.strftime('%H:%M'),
                    'action': 'SELL_CALL',
                    'underlying_price': current_price,
                    'strike': strike_price,
                    'premium': option_premium,
                    'signal_type': 'BEARISH',
                    'rsi': market_analysis.get('rsi', 0),
                    'supertrend': market_analysis.get('supertrend', 0)
                }

                self.simulation_results['trades'].append(trade_record)

                # Simulate order execution
                result = self.order_manager.sell_option(
                    underlying=self.instrument,
                    strike=strike_price,
                    option_type="CE",
                    quantity=self.config.get('POSITION_SIZE', 75)
                )

                if result['status'] == 'success':
                    # Override the simulated premium
                    position_id = result['position_id']
                    self.order_manager.positions[position_id]['entry_price'] = option_premium

                    self.logger.info(f"✅ SELL CALL executed: Strike ₹{strike_price} @ ₹{option_premium:.1f}")

                return result

            elif action == TradeAction.EXIT_POSITION:
                # Close all positions
                result = self.order_manager.close_all_positions()

                if result['status'] == 'success':
                    total_pnl = result.get('total_pnl', 0)
                    self.logger.info(f"🕒 All positions closed | P&L: ₹{total_pnl:.2f}")

                return result

            return {'status': 'no_action'}

        except Exception as e:
            self.logger.error(f"Error simulating trade execution: {e}")
            return {'status': 'error', 'message': str(e)}

    def _simulate_position_management(self, current_time: datetime, current_data: pd.DataFrame):
        """Simulate position management and stop loss checking"""
        try:
            if not self.order_manager.positions:
                return

            current_price = current_data.iloc[-1]['close']

            # Check each open position
            for position_id, position in list(self.order_manager.positions.items()):
                if position['status'].value != 'OPEN':
                    continue

                # Simulate current option premium
                strike = position['strike']
                option_type = position['option_type']
                current_premium = self.simulate_option_pricing(current_price, strike, option_type)

                # Check stop loss
                entry_premium = position['entry_price']
                loss_percentage = ((current_premium - entry_premium) / entry_premium) * 100

                if loss_percentage > self.order_manager.stop_loss_percentage:
                    # Trigger stop loss
                    close_result = self.order_manager.close_position(position_id)

                    if close_result['status'] == 'success':
                        pnl = close_result.get('pnl', 0)
                        self.logger.warning(f"🛑 STOP LOSS: {position_id} | Loss: {loss_percentage:.1f}% | P&L: ₹{pnl:.2f}")

                        # Record stop loss event
                        self.simulation_results['trades'].append({
                            'time': current_time.strftime('%H:%M'),
                            'action': 'STOP_LOSS',
                            'position_id': position_id,
                            'loss_percentage': loss_percentage,
                            'pnl': pnl
                        })

            # Check for end-of-day exit (3:15 PM)
            if current_time.time() >= dt_time(15, 15):
                if self.order_manager.positions:
                    exit_result = self.order_manager.close_all_positions()
                    if exit_result['status'] == 'success':
                        total_pnl = exit_result.get('total_pnl', 0)
                        self.logger.info(f"🕒 End of day exit | Total P&L: ₹{total_pnl:.2f}")

        except Exception as e:
            self.logger.error(f"Error in position management: {e}")

    def _generate_simulation_report(self) -> Dict:
        """Generate comprehensive simulation report"""
        try:
            # Get final positions summary
            positions_summary = self.order_manager.get_positions_summary()

            # Calculate statistics
            total_trades = len([t for t in self.simulation_results['trades'] if t.get('action') in ['SELL_PUT', 'SELL_CALL']])
            total_pnl = positions_summary.get('daily_pnl', 0)

            # Analyze trades
            profitable_trades = 0
            losing_trades = 0
            total_profit = 0
            total_loss = 0

            for position in positions_summary.get('closed_positions', []):
                pnl = position.get('pnl', 0)
                if pnl > 0:
                    profitable_trades += 1
                    total_profit += pnl
                else:
                    losing_trades += 1
                    total_loss += abs(pnl)

            win_rate = (profitable_trades / max(total_trades, 1)) * 100

            report = {
                'simulation_date': self.simulation_date.strftime('%Y-%m-%d'),
                'instrument': self.instrument,
                'strategy_config': {
                    'timeframe': self.timeframe,
                    'stop_loss_percentage': self.order_manager.stop_loss_percentage,
                    'supertrend_period': self.strategy.supertrend_period,
                    'supertrend_multiplier': self.strategy.supertrend_multiplier,
                    'rsi_period': self.strategy.rsi_period,
                    'rsi_buy_threshold': self.strategy.rsi_buy_threshold,
                    'rsi_sell_threshold': self.strategy.rsi_sell_threshold
                },
                'trading_statistics': {
                    'total_trades': total_trades,
                    'profitable_trades': profitable_trades,
                    'losing_trades': losing_trades,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl,
                    'total_profit': total_profit,
                    'total_loss': total_loss,
                    'average_profit': total_profit / max(profitable_trades, 1),
                    'average_loss': total_loss / max(losing_trades, 1)
                },
                'trades_detail': self.simulation_results['trades'],
                'timeline': self.simulation_results['timeline']
            }

            return report

        except Exception as e:
            self.logger.error(f"Error generating simulation report: {e}")
            return {'error': str(e)}

    def simulate_trading_day(self, date: datetime) -> Dict:
        """
        Simulate a complete trading day

        Args:
            date: Date to simulate

        Returns:
            Simulation results
        """
        try:
            self.simulation_date = date
            self.logger.info(f"🚀 Starting simulation for {date.strftime('%Y-%m-%d')}")

            # Reset daily counters
            self.strategy.reset_daily_counters()
            self.order_manager.reset_daily_data()
            self.simulation_results = {
                'trades': [],
                'signals': [],
                'pnl_history': [],
                'daily_stats': {},
                'timeline': []
            }

            # Get historical data for the day
            historical_data = self.get_historical_data_for_date(date)

            if historical_data.empty:
                return {'status': 'error', 'message': 'No historical data available'}

            # Simulate trading throughout the day
            simulation_results = self._run_intraday_simulation(historical_data)

            # Generate final report
            final_report = self._generate_simulation_report()

            self.logger.info(f"✅ Simulation completed for {date.strftime('%Y-%m-%d')}")

            return {
                'status': 'success',
                'date': date.strftime('%Y-%m-%d'),
                'simulation_results': simulation_results,
                'final_report': final_report
            }

        except Exception as e:
            self.logger.error(f"Error in trading day simulation: {e}")
            return {'status': 'error', 'message': str(e)}

    def _run_intraday_simulation(self, historical_data: pd.DataFrame) -> Dict:
        """
        Run the actual intraday simulation

        Args:
            historical_data: Historical OHLC data for the day

        Returns:
            Simulation results
        """
        try:
            total_candles = len(historical_data)
            signals_generated = 0
            trades_executed = 0

            self.logger.info(f"📊 Processing {total_candles} candles...")

            # Process each candle (simulating real-time)
            for i in range(50, total_candles):  # Start after 50 candles for indicators
                current_time = historical_data.index[i]

                # Get data up to current point (simulating real-time)
                current_data = historical_data.iloc[:i+1]

                # Check if it's a valid trading time (interval boundary)
                if not self._is_valid_simulation_time(current_time):
                    continue

                # Analyze market condition
                market_analysis = self.strategy.analyze_market_condition(current_data)

                if 'error' in market_analysis:
                    continue

                # Generate trade signal
                trade_signal = self.strategy.generate_trade_signal(market_analysis, current_time)

                # Log the analysis
                self._log_market_state(current_time, market_analysis, trade_signal)

                if trade_signal['action'] != TradeAction.NO_ACTION:
                    signals_generated += 1

                    # Execute trade simulation
                    execution_result = self._simulate_trade_execution(
                        trade_signal, market_analysis, current_time
                    )

                    if execution_result.get('status') == 'success':
                        trades_executed += 1

                # Check for position management
                self._simulate_position_management(current_time, current_data)

                # Update timeline
                self.simulation_results['timeline'].append({
                    'time': current_time.strftime('%H:%M'),
                    'price': market_analysis.get('current_price', 0),
                    'signal': market_analysis.get('signal', SignalType.NEUTRAL).value,
                    'action': trade_signal['action'].value,
                    'positions': len([p for p in self.order_manager.positions.values()
                                    if p['status'].value == 'OPEN'])
                })

            return {
                'total_candles_processed': total_candles,
                'signals_generated': signals_generated,
                'trades_executed': trades_executed,
                'final_positions': len(self.order_manager.positions)
            }

        except Exception as e:
            self.logger.error(f"Error in intraday simulation: {e}")
            return {'error': str(e)}

def print_simulation_report(report: Dict):
    """Print a formatted simulation report"""
    print("\n" + "=" * 80)
    print("📊 BACKTESTING SIMULATION REPORT")
    print("=" * 80)

    print(f"📅 Date: {report['simulation_date']}")
    print(f"🎯 Instrument: {report['instrument']}")

    config = report['strategy_config']
    print(f"⚙️  Strategy: SuperTrend({config['supertrend_period']}, {config['supertrend_multiplier']}) + RSI({config['rsi_period']})")
    print(f"🛡️  Stop Loss: {config['stop_loss_percentage']}%")
    print(f"⏰ Timeframe: {config['timeframe']}")

    stats = report['trading_statistics']
    print(f"\n📈 TRADING RESULTS:")
    print(f"   Total Trades: {stats['total_trades']}")
    print(f"   Profitable: {stats['profitable_trades']} | Losing: {stats['losing_trades']}")
    print(f"   Win Rate: {stats['win_rate']:.1f}%")
    print(f"   Total P&L: ₹{stats['total_pnl']:.2f}")

    if stats['profitable_trades'] > 0:
        print(f"   Average Profit: ₹{stats['average_profit']:.2f}")
    if stats['losing_trades'] > 0:
        print(f"   Average Loss: ₹{stats['average_loss']:.2f}")

    print(f"\n📋 TRADE DETAILS:")
    for i, trade in enumerate(report['trades_detail'], 1):
        if trade.get('action') in ['SELL_PUT', 'SELL_CALL']:
            print(f"   {i}. {trade['time']} | {trade['action']} | Strike: ₹{trade['strike']} | Premium: ₹{trade['premium']:.1f}")

    print("=" * 80)

def create_simulation_config() -> Dict:
    """Create configuration for simulation"""
    load_dotenv()

    return {
        'ZERODHA_API_KEY': os.getenv('ZERODHA_API_KEY'),
        'ZERODHA_ACCESS_TOKEN': os.getenv('ZERODHA_ACCESS_TOKEN'),
        'TRADING_MODE': 'TESTING',
        'TIMEFRAME': os.getenv('TIMEFRAME', '5minute'),
        'INSTRUMENT': os.getenv('INSTRUMENT', 'NIFTY'),
        'SUPERTREND_PERIOD': int(os.getenv('SUPERTREND_PERIOD', 5)),
        'SUPERTREND_MULTIPLIER': float(os.getenv('SUPERTREND_MULTIPLIER', 3.0)),
        'RSI_PERIOD': int(os.getenv('RSI_PERIOD', 14)),
        'RSI_BUY_THRESHOLD': float(os.getenv('RSI_BUY_THRESHOLD', 60)),
        'RSI_SELL_THRESHOLD': float(os.getenv('RSI_SELL_THRESHOLD', 45)),
        'MAX_TRADES_PER_DAY': int(os.getenv('MAX_TRADES_PER_DAY', 2)),
        'STOP_LOSS_PERCENTAGE': float(os.getenv('STOP_LOSS_PERCENTAGE', 20)),
        'POSITION_SIZE': int(os.getenv('POSITION_SIZE', 75))
    }

def main():
    """Main function to run backtesting simulation"""
    try:
        print("🧪 SuperTrend + RSI Strategy Backtesting Simulator")
        print("=" * 60)

        # Create configuration
        config = create_simulation_config()

        # Initialize simulator
        simulator = BacktestSimulator(config)

        if not simulator.initialize_connections():
            print("❌ Failed to initialize connections")
            return

        # Get simulation date (default: yesterday)
        simulation_date = datetime.now() - timedelta(days=1)

        # You can also specify a specific date
        # simulation_date = datetime(2024, 1, 15)  # Example: January 15, 2024

        print(f"🎯 Simulating trading day: {simulation_date.strftime('%Y-%m-%d')}")
        print(f"📊 Instrument: {config['INSTRUMENT']}")
        print(f"⏰ Timeframe: {config['TIMEFRAME']}")
        print(f"🛡️ Stop Loss: {config['STOP_LOSS_PERCENTAGE']}%")

        # Run simulation
        result = simulator.simulate_trading_day(simulation_date)

        if result['status'] == 'success':
            # Print detailed report
            print_simulation_report(result['final_report'])

            print("\n🎯 Simulation completed successfully!")
            print("💡 Try different dates or modify strategy parameters in .env file")

        else:
            print(f"❌ Simulation failed: {result.get('message', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error running simulation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()