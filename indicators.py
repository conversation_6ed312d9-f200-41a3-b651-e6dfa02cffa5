"""
Technical Indicators Module for SuperTrend and RSI Strategy
Implements SuperTrend and RSI calculations with proper data validation
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Class to calculate technical indicators for trading strategy"""

    def __init__(self):
        self.logger = logger

    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Average True Range (ATR)

        Args:
            high: High prices series
            low: Low prices series
            close: Close prices series
            period: ATR period (default 14)

        Returns:
            ATR values as pandas Series
        """
        try:
            # Calculate True Range components
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))

            # True Range is the maximum of the three
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

            # Calculate ATR as moving average of True Range
            atr = true_range.rolling(window=period).mean()

            return atr

        except Exception as e:
            self.logger.error(f"Error calculating ATR: {e}")
            return pd.Series(dtype=float)

    def calculate_supertrend(self, high: pd.Series, low: pd.Series, close: pd.Series,
                           period: int = 10, multiplier: float = 3.0) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate SuperTrend indicator

        Args:
            high: High prices series
            low: Low prices series
            close: Close prices series
            period: ATR period (default 10)
            multiplier: ATR multiplier (default 3.0)

        Returns:
            Tuple of (supertrend_values, supertrend_direction)
            supertrend_direction: 1 for uptrend, -1 for downtrend
        """
        try:
            # Calculate ATR
            atr = self.calculate_atr(high, low, close, period)

            # Calculate basic upper and lower bands
            hl2 = (high + low) / 2
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)

            # Initialize arrays
            supertrend = pd.Series(index=close.index, dtype=float)
            direction = pd.Series(index=close.index, dtype=int)

            # Calculate SuperTrend
            for i in range(1, len(close)):
                # Current upper and lower bands
                curr_upper = upper_band.iloc[i]
                curr_lower = lower_band.iloc[i]
                prev_upper = upper_band.iloc[i-1] if i > 0 else curr_upper
                prev_lower = lower_band.iloc[i-1] if i > 0 else curr_lower

                # Adjust bands based on previous values
                if curr_upper < prev_upper or close.iloc[i-1] > prev_upper:
                    final_upper = curr_upper
                else:
                    final_upper = prev_upper

                if curr_lower > prev_lower or close.iloc[i-1] < prev_lower:
                    final_lower = curr_lower
                else:
                    final_lower = prev_lower

                # Determine SuperTrend direction
                if i == 1:
                    direction.iloc[i] = 1 if close.iloc[i] <= final_lower else -1
                    supertrend.iloc[i] = final_lower if direction.iloc[i] == 1 else final_upper
                else:
                    prev_direction = direction.iloc[i-1]

                    if prev_direction == 1 and close.iloc[i] > final_lower:
                        direction.iloc[i] = -1
                        supertrend.iloc[i] = final_upper
                    elif prev_direction == -1 and close.iloc[i] < final_upper:
                        direction.iloc[i] = 1
                        supertrend.iloc[i] = final_lower
                    else:
                        direction.iloc[i] = prev_direction
                        supertrend.iloc[i] = final_lower if direction.iloc[i] == 1 else final_upper

            return supertrend, direction

        except Exception as e:
            self.logger.error(f"Error calculating SuperTrend: {e}")
            return pd.Series(dtype=float), pd.Series(dtype=int)

    def calculate_rsi(self, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)

        Args:
            close: Close prices series
            period: RSI period (default 14)

        Returns:
            RSI values as pandas Series
        """
        try:
            # Calculate price changes
            delta = close.diff()

            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)

            # Calculate average gains and losses
            avg_gains = gains.rolling(window=period).mean()
            avg_losses = losses.rolling(window=period).mean()

            # Calculate RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception as e:
            self.logger.error(f"Error calculating RSI: {e}")
            return pd.Series(dtype=float)

    def get_trading_signals(self, data: pd.DataFrame, supertrend_period: int = 10,
                          supertrend_multiplier: float = 3.0, rsi_period: int = 14,
                          rsi_buy_threshold: float = 60, rsi_sell_threshold: float = 45) -> pd.DataFrame:
        """
        Generate trading signals based on SuperTrend and RSI

        Args:
            data: DataFrame with OHLC data
            supertrend_period: SuperTrend ATR period
            supertrend_multiplier: SuperTrend multiplier
            rsi_period: RSI period
            rsi_buy_threshold: RSI threshold for bullish signal
            rsi_sell_threshold: RSI threshold for bearish signal

        Returns:
            DataFrame with signals and indicator values
        """
        try:
            # Validate input data
            required_columns = ['high', 'low', 'close']
            if not all(col in data.columns for col in required_columns):
                raise ValueError(f"Data must contain columns: {required_columns}")

            result = data.copy()

            # Calculate indicators
            supertrend, st_direction = self.calculate_supertrend(
                data['high'], data['low'], data['close'],
                supertrend_period, supertrend_multiplier
            )

            rsi = self.calculate_rsi(data['close'], rsi_period)

            # Add indicators to result
            result['supertrend'] = supertrend
            result['st_direction'] = st_direction
            result['rsi'] = rsi

            # Generate signals
            result['bullish_signal'] = (
                (data['close'] > supertrend) &
                (rsi > rsi_buy_threshold)
            )

            result['bearish_signal'] = (
                (data['close'] < supertrend) &
                (rsi < rsi_sell_threshold)
            )

            # Add signal strength
            result['signal_strength'] = 0
            result.loc[result['bullish_signal'], 'signal_strength'] = 1
            result.loc[result['bearish_signal'], 'signal_strength'] = -1

            return result

        except Exception as e:
            self.logger.error(f"Error generating trading signals: {e}")
            return pd.DataFrame()

def validate_ohlc_data(data: pd.DataFrame) -> bool:
    """
    Validate OHLC data format and quality

    Args:
        data: DataFrame with OHLC data

    Returns:
        True if data is valid, False otherwise
    """
    try:
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_columns):
            logger.error(f"Missing required columns. Need: {required_columns}")
            return False

        # Check for sufficient data
        if len(data) < 50:
            logger.error("Insufficient data points. Need at least 50 candles")
            return False

        # Check for valid OHLC relationships
        invalid_ohlc = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close'])
        )

        if invalid_ohlc.any():
            logger.error("Invalid OHLC relationships found in data")
            return False

        # Check for missing values
        if data[required_columns].isnull().any().any():
            logger.error("Missing values found in OHLC data")
            return False

        return True

    except Exception as e:
        logger.error(f"Error validating OHLC data: {e}")
        return False