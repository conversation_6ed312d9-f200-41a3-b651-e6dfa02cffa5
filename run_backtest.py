"""
Simple script to run backtesting simulations
"""

import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

from backtest_simulator import BacktestSimulator, create_simulation_config, print_simulation_report

def run_single_day_backtest():
    """Run backtest for a single day"""
    print("🧪 Single Day Backtesting")
    print("=" * 50)

    # Create configuration
    config = create_simulation_config()

    # Initialize simulator
    simulator = BacktestSimulator(config)

    if not simulator.initialize_connections():
        print("❌ Failed to initialize connections")
        return

    # Choose simulation date (yesterday by default)
    simulation_date = datetime.now() - timedelta(days=1)

    # Or specify a specific date for testing
    # simulation_date = datetime(2024, 1, 15)  # Example date

    print(f"📅 Simulating: {simulation_date.strftime('%Y-%m-%d')}")
    print(f"🎯 Instrument: {config['INSTRUMENT']}")

    # Run simulation
    result = simulator.simulate_trading_day(simulation_date)

    if result['status'] == 'success':
        print_simulation_report(result['final_report'])
        return result['final_report']
    else:
        print(f"❌ Simulation failed: {result.get('message')}")
        return None

def run_multi_day_backtest(days: int = 5):
    """Run backtest for multiple days"""
    print(f"\n🧪 Multi-Day Backtesting ({days} days)")
    print("=" * 50)

    # Create configuration
    config = create_simulation_config()

    # Initialize simulator
    simulator = BacktestSimulator(config)

    if not simulator.initialize_connections():
        print("❌ Failed to initialize connections")
        return

    results = []
    total_pnl = 0
    successful_days = 0

    # Run simulation for multiple days
    for i in range(days):
        simulation_date = datetime.now() - timedelta(days=i+1)

        # Skip weekends
        if simulation_date.weekday() >= 5:
            continue

        print(f"\n📅 Day {i+1}: {simulation_date.strftime('%Y-%m-%d')}")

        result = simulator.simulate_trading_day(simulation_date)

        if result['status'] == 'success':
            report = result['final_report']
            day_pnl = report['trading_statistics']['total_pnl']
            total_pnl += day_pnl
            successful_days += 1

            print(f"   📊 Trades: {report['trading_statistics']['total_trades']}")
            print(f"   💰 P&L: ₹{day_pnl:.2f}")
            print(f"   📈 Win Rate: {report['trading_statistics']['win_rate']:.1f}%")

            results.append(report)
        else:
            print(f"   ❌ Failed: {result.get('message')}")

    # Print summary
    if results:
        print(f"\n📊 MULTI-DAY SUMMARY:")
        print(f"   Successful Days: {successful_days}/{days}")
        print(f"   Total P&L: ₹{total_pnl:.2f}")
        print(f"   Average P&L per Day: ₹{total_pnl/max(successful_days, 1):.2f}")

        # Calculate overall statistics
        total_trades = sum(r['trading_statistics']['total_trades'] for r in results)
        total_profitable = sum(r['trading_statistics']['profitable_trades'] for r in results)
        overall_win_rate = (total_profitable / max(total_trades, 1)) * 100

        print(f"   Total Trades: {total_trades}")
        print(f"   Overall Win Rate: {overall_win_rate:.1f}%")

    return results

def test_different_configurations():
    """Test different strategy configurations"""
    print(f"\n🧪 Testing Different Configurations")
    print("=" * 50)

    # Base configuration
    base_config = create_simulation_config()

    # Test configurations
    test_configs = [
        {
            'name': 'Conservative (15% SL)',
            'changes': {'STOP_LOSS_PERCENTAGE': 15}
        },
        {
            'name': 'Aggressive (30% SL)',
            'changes': {'STOP_LOSS_PERCENTAGE': 30}
        },
        {
            'name': 'Tight RSI (65/40)',
            'changes': {'RSI_BUY_THRESHOLD': 65, 'RSI_SELL_THRESHOLD': 40}
        },
        {
            'name': 'Loose RSI (55/50)',
            'changes': {'RSI_BUY_THRESHOLD': 55, 'RSI_SELL_THRESHOLD': 50}
        }
    ]

    simulation_date = datetime.now() - timedelta(days=1)
    results = []

    for test_config in test_configs:
        print(f"\n🎯 Testing: {test_config['name']}")

        # Create modified configuration
        config = base_config.copy()
        config.update(test_config['changes'])

        # Run simulation
        simulator = BacktestSimulator(config)
        if simulator.initialize_connections():
            result = simulator.simulate_trading_day(simulation_date)

            if result['status'] == 'success':
                report = result['final_report']
                stats = report['trading_statistics']

                print(f"   📊 Trades: {stats['total_trades']}")
                print(f"   💰 P&L: ₹{stats['total_pnl']:.2f}")
                print(f"   📈 Win Rate: {stats['win_rate']:.1f}%")

                results.append({
                    'name': test_config['name'],
                    'pnl': stats['total_pnl'],
                    'trades': stats['total_trades'],
                    'win_rate': stats['win_rate']
                })
            else:
                print(f"   ❌ Failed: {result.get('message')}")

    # Compare results
    if results:
        print(f"\n📊 CONFIGURATION COMPARISON:")
        print(f"{'Configuration':<20} {'P&L':<10} {'Trades':<8} {'Win Rate':<10}")
        print("-" * 50)

        for result in results:
            print(f"{result['name']:<20} ₹{result['pnl']:<9.2f} {result['trades']:<8} {result['win_rate']:<9.1f}%")

    return results

def main():
    """Main function"""
    print("🚀 SuperTrend + RSI Strategy Backtesting Suite")
    print("=" * 60)

    try:
        # Run single day backtest
        single_result = run_single_day_backtest()

        if single_result:
            # Run multi-day backtest
            multi_results = run_multi_day_backtest(5)

            # Test different configurations
            config_results = test_different_configurations()

            print("\n" + "=" * 60)
            print("✅ Backtesting completed successfully!")
            print("\n💡 Key Insights:")
            print("   • Test different dates to see strategy performance")
            print("   • Modify .env parameters to optimize strategy")
            print("   • Use results to fine-tune risk management")
            print("   • Compare different instruments and timeframes")

            print("\n🎯 Next Steps:")
            print("   1. Analyze the results and identify patterns")
            print("   2. Optimize strategy parameters based on backtests")
            print("   3. Test on different market conditions")
            print("   4. Run live simulation when confident")

    except Exception as e:
        print(f"❌ Error in backtesting: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()