"""
Order Management System for Options Trading
Handles order placement, position tracking, and risk management
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum
import logging
from kiteconnect import KiteConnect
from kiteconnect.exceptions import KiteException

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    PLACED = "PLACED"
    EXECUTED = "EXECUTED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class PositionStatus(Enum):
    """Position status enumeration"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIAL = "PARTIAL"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    SL = "SL"
    SL_M = "SL-M"

class TransactionType(Enum):
    """Transaction type enumeration"""
    BUY = "BUY"
    SELL = "SELL"

class OrderManager:
    """
    Manages option orders, positions, and risk management
    """

    def __init__(self, kite: KiteConnect, trading_mode: str = "TESTING"):
        """
        Initialize Order Manager

        Args:
            kite: KiteConnect instance
            trading_mode: "TESTING" or "LIVE"
        """
        self.kite = kite
        self.trading_mode = trading_mode.upper()
        self.logger = logger

        # Position tracking
        self.positions = {}
        self.orders = {}
        self.daily_pnl = 0.0

        # Risk limits
        self.max_loss_per_trade = 0.0
        self.max_daily_loss = 0.0

        self.logger.info(f"Order Manager initialized in {self.trading_mode} mode")

    def get_option_symbol(self, underlying: str, expiry: str, strike: float, option_type: str) -> str:
        """
        Generate option symbol for Zerodha

        Args:
            underlying: Underlying symbol (e.g., "NIFTY")
            expiry: Expiry date in YYYYMMDD format
            strike: Strike price
            option_type: "CE" or "PE"

        Returns:
            Option symbol string
        """
        try:
            # Format: NIFTY24JAN18000CE
            # Convert expiry date to required format
            expiry_date = datetime.strptime(expiry, "%Y%m%d")
            month_year = expiry_date.strftime("%y%b").upper()

            # Format strike price (remove decimal if whole number)
            strike_str = str(int(strike)) if strike == int(strike) else str(strike)

            symbol = f"{underlying}{month_year}{strike_str}{option_type}"
            return symbol

        except Exception as e:
            self.logger.error(f"Error generating option symbol: {e}")
            return ""

    def get_option_chain(self, underlying: str = "NIFTY") -> Dict:
        """
        Get option chain data

        Args:
            underlying: Underlying symbol

        Returns:
            Option chain data
        """
        try:
            if self.trading_mode == "TESTING":
                # Return mock data for testing
                return {
                    "status": "success",
                    "data": {
                        "underlying_value": 21500.0,
                        "options": []
                    }
                }

            # Get instruments for options
            instruments = self.kite.instruments("NFO")
            option_instruments = [
                inst for inst in instruments
                if inst['name'] == underlying and inst['instrument_type'] in ['CE', 'PE']
            ]

            return {
                "status": "success",
                "data": {
                    "instruments": option_instruments
                }
            }

        except Exception as e:
            self.logger.error(f"Error fetching option chain: {e}")
            return {"status": "error", "message": str(e)}

    def place_option_order(self, symbol: str, quantity: int, transaction_type: TransactionType,
                          order_type: OrderType = OrderType.MARKET, price: float = 0.0) -> Dict:
        """
        Place option order

        Args:
            symbol: Option symbol
            quantity: Quantity to trade
            transaction_type: BUY or SELL
            order_type: Order type (MARKET, LIMIT, etc.)
            price: Limit price (for limit orders)

        Returns:
            Order response dictionary
        """
        try:
            order_id = str(uuid.uuid4())

            order_data = {
                "order_id": order_id,
                "symbol": symbol,
                "quantity": quantity,
                "transaction_type": transaction_type.value,
                "order_type": order_type.value,
                "price": price,
                "timestamp": datetime.now(),
                "status": OrderStatus.PENDING
            }

            if self.trading_mode == "TESTING":
                # Simulate order placement
                order_data["status"] = OrderStatus.EXECUTED
                order_data["average_price"] = price if price > 0 else 100.0  # Mock price
                order_data["filled_quantity"] = quantity

                self.logger.info(f"SIMULATED ORDER: {transaction_type.value} {quantity} {symbol} @ {order_data['average_price']}")

            else:
                # Place real order
                try:
                    response = self.kite.place_order(
                        variety=self.kite.VARIETY_REGULAR,
                        exchange=self.kite.EXCHANGE_NFO,
                        tradingsymbol=symbol,
                        transaction_type=transaction_type.value,
                        quantity=quantity,
                        product=self.kite.PRODUCT_MIS,  # Intraday
                        order_type=order_type.value,
                        price=price if order_type == OrderType.LIMIT else None
                    )

                    order_data["zerodha_order_id"] = response["order_id"]
                    order_data["status"] = OrderStatus.PLACED

                    self.logger.info(f"ORDER PLACED: {response['order_id']} - {transaction_type.value} {quantity} {symbol}")

                except KiteException as e:
                    order_data["status"] = OrderStatus.REJECTED
                    order_data["error"] = str(e)
                    self.logger.error(f"Order rejected: {e}")

            # Store order
            self.orders[order_id] = order_data

            return {
                "status": "success" if order_data["status"] != OrderStatus.REJECTED else "error",
                "order_id": order_id,
                "order_data": order_data
            }

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def sell_option(self, underlying: str, strike: float, option_type: str,
                   quantity: int, expiry: str = None) -> Dict:
        """
        Sell option (main strategy action)

        Args:
            underlying: Underlying symbol
            strike: Strike price
            option_type: "CE" or "PE"
            quantity: Quantity to sell
            expiry: Expiry date (if None, uses nearest expiry)

        Returns:
            Order response
        """
        try:
            # Get expiry if not provided
            if not expiry:
                expiry = self.get_nearest_expiry()

            # Generate option symbol
            symbol = self.get_option_symbol(underlying, expiry, strike, option_type)

            if not symbol:
                return {"status": "error", "message": "Failed to generate option symbol"}

            # Place sell order
            response = self.place_option_order(
                symbol=symbol,
                quantity=quantity,
                transaction_type=TransactionType.SELL,
                order_type=OrderType.MARKET
            )

            if response["status"] == "success":
                # Create position entry
                position_id = str(uuid.uuid4())
                position_data = {
                    "position_id": position_id,
                    "symbol": symbol,
                    "underlying": underlying,
                    "strike": strike,
                    "option_type": option_type,
                    "quantity": quantity,
                    "entry_price": response["order_data"].get("average_price", 0),
                    "entry_time": datetime.now(),
                    "status": PositionStatus.OPEN,
                    "order_id": response["order_id"]
                }

                self.positions[position_id] = position_data

                self.logger.info(f"POSITION OPENED: {position_id} - SELL {quantity} {symbol}")

                response["position_id"] = position_id
                response["position_data"] = position_data

            return response

        except Exception as e:
            self.logger.error(f"Error selling option: {e}")
            return {"status": "error", "message": str(e)}

    def close_position(self, position_id: str) -> Dict:
        """
        Close an open position

        Args:
            position_id: Position identifier

        Returns:
            Close order response
        """
        try:
            if position_id not in self.positions:
                return {"status": "error", "message": "Position not found"}

            position = self.positions[position_id]

            if position["status"] != PositionStatus.OPEN:
                return {"status": "error", "message": "Position is not open"}

            # Place buy order to close short position
            response = self.place_option_order(
                symbol=position["symbol"],
                quantity=position["quantity"],
                transaction_type=TransactionType.BUY,  # Buy to close short position
                order_type=OrderType.MARKET
            )

            if response["status"] == "success":
                # Update position
                position["exit_price"] = response["order_data"].get("average_price", 0)
                position["exit_time"] = datetime.now()
                position["status"] = PositionStatus.CLOSED
                position["close_order_id"] = response["order_id"]

                # Calculate P&L
                entry_price = position["entry_price"]
                exit_price = position["exit_price"]
                quantity = position["quantity"]

                # For short options: P&L = (Entry Price - Exit Price) * Quantity
                pnl = (entry_price - exit_price) * quantity
                position["pnl"] = pnl
                self.daily_pnl += pnl

                self.logger.info(f"POSITION CLOSED: {position_id} - P&L: ₹{pnl:.2f}")

                response["position_data"] = position
                response["pnl"] = pnl

            return response

        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return {"status": "error", "message": str(e)}

    def close_all_positions(self) -> Dict:
        """
        Close all open positions

        Returns:
            Summary of close operations
        """
        try:
            results = []
            total_pnl = 0.0

            open_positions = [
                pos_id for pos_id, pos in self.positions.items()
                if pos["status"] == PositionStatus.OPEN
            ]

            for position_id in open_positions:
                result = self.close_position(position_id)
                results.append({
                    "position_id": position_id,
                    "result": result
                })

                if result["status"] == "success":
                    total_pnl += result.get("pnl", 0)

            return {
                "status": "success",
                "closed_positions": len(results),
                "total_pnl": total_pnl,
                "results": results
            }

        except Exception as e:
            self.logger.error(f"Error closing all positions: {e}")
            return {"status": "error", "message": str(e)}

    def get_position_pnl(self, position_id: str, current_price: float = None) -> float:
        """
        Calculate current P&L for a position

        Args:
            position_id: Position identifier
            current_price: Current option price (if None, fetches from market)

        Returns:
            Current P&L value
        """
        try:
            if position_id not in self.positions:
                return 0.0

            position = self.positions[position_id]

            if position["status"] != PositionStatus.OPEN:
                return position.get("pnl", 0.0)

            if current_price is None:
                current_price = self.get_current_option_price(position["symbol"])

            entry_price = position["entry_price"]
            quantity = position["quantity"]

            # For short options: P&L = (Entry Price - Current Price) * Quantity
            pnl = (entry_price - current_price) * quantity

            return pnl

        except Exception as e:
            self.logger.error(f"Error calculating position P&L: {e}")
            return 0.0

    def get_current_option_price(self, symbol: str) -> float:
        """
        Get current market price for option

        Args:
            symbol: Option symbol

        Returns:
            Current market price
        """
        try:
            if self.trading_mode == "TESTING":
                # Return mock price for testing
                return 50.0

            # Get LTP from Zerodha
            quote = self.kite.ltp([f"NFO:{symbol}"])
            return quote[f"NFO:{symbol}"]["last_price"]

        except Exception as e:
            self.logger.error(f"Error fetching option price for {symbol}: {e}")
            return 0.0

    def get_nearest_expiry(self) -> str:
        """
        Get nearest Thursday expiry date

        Returns:
            Expiry date in YYYYMMDD format
        """
        try:
            today = datetime.now().date()

            # Find next Thursday
            days_ahead = 3 - today.weekday()  # Thursday is weekday 3
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7

            next_thursday = today + timedelta(days=days_ahead)
            return next_thursday.strftime("%Y%m%d")

        except Exception as e:
            self.logger.error(f"Error calculating nearest expiry: {e}")
            # Return a default date
            return (datetime.now() + timedelta(days=7)).strftime("%Y%m%d")

    def check_risk_limits(self, position_id: str = None) -> Dict:
        """
        Check risk limits for positions

        Args:
            position_id: Specific position to check (if None, checks all)

        Returns:
            Risk check results
        """
        try:
            risk_alerts = []

            if position_id:
                # Check specific position
                if position_id in self.positions:
                    position = self.positions[position_id]
                    current_price = self.get_current_option_price(position["symbol"])
                    pnl = self.get_position_pnl(position_id, current_price)

                    # Check stop loss
                    entry_price = position["entry_price"]
                    loss_percentage = ((current_price - entry_price) / entry_price) * 100

                    if loss_percentage > 20:  # 20% stop loss
                        risk_alerts.append({
                            "position_id": position_id,
                            "alert_type": "STOP_LOSS",
                            "message": f"Stop loss triggered: {loss_percentage:.2f}%",
                            "current_pnl": pnl
                        })
            else:
                # Check all positions
                for pos_id, position in self.positions.items():
                    if position["status"] == PositionStatus.OPEN:
                        current_price = self.get_current_option_price(position["symbol"])
                        pnl = self.get_position_pnl(pos_id, current_price)

                        entry_price = position["entry_price"]
                        loss_percentage = ((current_price - entry_price) / entry_price) * 100

                        if loss_percentage > 20:
                            risk_alerts.append({
                                "position_id": pos_id,
                                "alert_type": "STOP_LOSS",
                                "message": f"Stop loss triggered: {loss_percentage:.2f}%",
                                "current_pnl": pnl
                            })

            return {
                "status": "success",
                "risk_alerts": risk_alerts,
                "daily_pnl": self.daily_pnl
            }

        except Exception as e:
            self.logger.error(f"Error checking risk limits: {e}")
            return {"status": "error", "message": str(e)}

    def get_positions_summary(self) -> Dict:
        """
        Get summary of all positions

        Returns:
            Positions summary
        """
        try:
            open_positions = []
            closed_positions = []
            total_pnl = 0.0

            for pos_id, position in self.positions.items():
                position_summary = {
                    "position_id": pos_id,
                    "symbol": position["symbol"],
                    "strike": position["strike"],
                    "option_type": position["option_type"],
                    "quantity": position["quantity"],
                    "entry_price": position["entry_price"],
                    "entry_time": position["entry_time"].strftime("%Y-%m-%d %H:%M:%S"),
                    "status": position["status"].value
                }

                if position["status"] == PositionStatus.OPEN:
                    current_price = self.get_current_option_price(position["symbol"])
                    current_pnl = self.get_position_pnl(pos_id, current_price)
                    position_summary["current_price"] = current_price
                    position_summary["current_pnl"] = current_pnl
                    open_positions.append(position_summary)
                    total_pnl += current_pnl
                else:
                    position_summary["exit_price"] = position.get("exit_price", 0)
                    position_summary["exit_time"] = position.get("exit_time", "").strftime("%Y-%m-%d %H:%M:%S") if position.get("exit_time") else ""
                    position_summary["pnl"] = position.get("pnl", 0)
                    closed_positions.append(position_summary)
                    total_pnl += position.get("pnl", 0)

            return {
                "status": "success",
                "open_positions": open_positions,
                "closed_positions": closed_positions,
                "total_positions": len(self.positions),
                "daily_pnl": total_pnl,
                "summary": {
                    "open_count": len(open_positions),
                    "closed_count": len(closed_positions),
                    "total_pnl": total_pnl
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting positions summary: {e}")
            return {"status": "error", "message": str(e)}

    def reset_daily_data(self):
        """Reset daily data (call at start of new trading day)"""
        try:
            self.positions.clear()
            self.orders.clear()
            self.daily_pnl = 0.0
            self.logger.info("Daily data reset completed")

        except Exception as e:
            self.logger.error(f"Error resetting daily data: {e}")

    def get_order_history(self) -> List[Dict]:
        """
        Get order history

        Returns:
            List of order records
        """
        try:
            order_history = []

            for order_id, order in self.orders.items():
                order_record = {
                    "order_id": order_id,
                    "symbol": order["symbol"],
                    "quantity": order["quantity"],
                    "transaction_type": order["transaction_type"],
                    "order_type": order["order_type"],
                    "price": order.get("price", 0),
                    "average_price": order.get("average_price", 0),
                    "status": order["status"].value,
                    "timestamp": order["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
                }

                if "error" in order:
                    order_record["error"] = order["error"]

                order_history.append(order_record)

            # Sort by timestamp
            order_history.sort(key=lambda x: x["timestamp"], reverse=True)

            return order_history

        except Exception as e:
            self.logger.error(f"Error getting order history: {e}")
            return []

def create_order_manager(kite: KiteConnect, trading_mode: str) -> OrderManager:
    """
    Factory function to create OrderManager instance

    Args:
        kite: KiteConnect instance
        trading_mode: Trading mode ("TESTING" or "LIVE")

    Returns:
        OrderManager instance
    """
    return OrderManager(kite, trading_mode)